# Generated by Django 5.2.4 on 2025-07-18 13:17

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('entrepreneurship', '0003_alter_milestone_options_alter_project_options_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='EquipmentCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('category_type', models.CharField(choices=[('3d_printing', 'Impression 3D'), ('laser_cutting', 'Découpe Laser'), ('cnc_milling', 'Fraiseuse CNC'), ('electronics', 'Électronique'), ('textiles', 'Textiles'), ('woodworking', 'Menuiserie'), ('metalworking', 'Mé<PERSON>lurgie'), ('computers', 'Informatique'), ('measurement', 'Mesure & Test'), ('hand_tools', 'Outils Manuels'), ('safety', 'Sécurité'), ('other', 'Autres')], max_length=20)),
                ('description', models.TextField()),
                ('icon', models.CharField(default='wrench-screwdriver', max_length=50)),
                ('color', models.CharField(default='#2E7D32', max_length=7)),
                ('requires_training', models.BooleanField(default=True)),
                ('safety_level', models.IntegerField(default=1, help_text='1=Faible, 5=Très élevé', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name_plural': 'Equipment Categories',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Equipment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('brand', models.CharField(blank=True, max_length=100)),
                ('model', models.CharField(blank=True, max_length=100)),
                ('serial_number', models.CharField(max_length=100, unique=True)),
                ('specifications', models.JSONField(blank=True, default=dict)),
                ('status', models.CharField(choices=[('available', 'Disponible'), ('in_use', "En cours d'utilisation"), ('reserved', 'Réservé'), ('maintenance', 'En maintenance'), ('broken', 'Hors service'), ('retired', 'Retiré du service')], default='available', max_length=20)),
                ('condition', models.CharField(choices=[('excellent', 'Excellent'), ('good', 'Bon'), ('fair', 'Correct'), ('poor', 'Mauvais'), ('broken', 'Cassé')], default='good', max_length=20)),
                ('location', models.CharField(help_text='Emplacement physique dans le lab', max_length=100)),
                ('qr_code', models.CharField(blank=True, help_text='Code QR pour identification rapide', max_length=100)),
                ('purchase_date', models.DateField()),
                ('purchase_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('warranty_expiry', models.DateField(blank=True, null=True)),
                ('last_maintenance', models.DateField(blank=True, null=True)),
                ('next_maintenance', models.DateField(blank=True, null=True)),
                ('maintenance_interval_days', models.IntegerField(default=90)),
                ('manual_url', models.URLField(blank=True, help_text="Lien vers le manuel d'utilisation")),
                ('training_materials', models.URLField(blank=True, help_text='Matériels de formation')),
                ('safety_instructions', models.TextField(blank=True)),
                ('image', models.ImageField(blank=True, null=True, upload_to='equipment/')),
                ('total_usage_hours', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('total_reservations', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('responsible_person', models.ForeignKey(blank=True, help_text='Responsable de cet équipement', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_equipment', to=settings.AUTH_USER_MODEL)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='equipment', to='lab_equipment.equipmentcategory')),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='EquipmentReservation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_time', models.DateTimeField()),
                ('end_time', models.DateTimeField()),
                ('duration_hours', models.DecimalField(decimal_places=2, editable=False, max_digits=5)),
                ('purpose', models.TextField(help_text="Objectif d'utilisation de l'équipement")),
                ('materials_needed', models.TextField(blank=True, help_text='Matériaux nécessaires')),
                ('expected_output', models.TextField(blank=True, help_text='Résultat attendu')),
                ('status', models.CharField(choices=[('pending', 'En attente'), ('approved', 'Approuvée'), ('active', 'En cours'), ('completed', 'Terminée'), ('cancelled', 'Annulée'), ('no_show', 'Absence non justifiée')], default='pending', max_length=20)),
                ('priority', models.CharField(choices=[('low', 'Basse'), ('normal', 'Normale'), ('high', 'Haute'), ('urgent', 'Urgente')], default='normal', max_length=20)),
                ('approved_at', models.DateTimeField(blank=True, null=True)),
                ('approval_notes', models.TextField(blank=True)),
                ('actual_start_time', models.DateTimeField(blank=True, null=True)),
                ('actual_end_time', models.DateTimeField(blank=True, null=True)),
                ('actual_duration_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('user_rating', models.IntegerField(blank=True, help_text='Note de satisfaction (1-5)', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('user_feedback', models.TextField(blank=True)),
                ('issues_reported', models.TextField(blank=True, help_text='Problèmes rencontrés')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_reservations', to=settings.AUTH_USER_MODEL)),
                ('equipment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reservations', to='lab_equipment.equipment')),
                ('project', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='equipment_reservations', to='entrepreneurship.project')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='equipment_reservations', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-start_time'],
                'unique_together': {('equipment', 'start_time', 'end_time')},
            },
        ),
        migrations.CreateModel(
            name='EquipmentTraining',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('training_type', models.CharField(choices=[('basic', 'Formation de base'), ('advanced', 'Formation avancée'), ('safety', 'Formation sécurité'), ('maintenance', 'Formation maintenance'), ('certification', 'Certification')], max_length=20)),
                ('description', models.TextField()),
                ('scheduled_date', models.DateTimeField()),
                ('duration_hours', models.DecimalField(decimal_places=2, max_digits=4)),
                ('max_participants', models.IntegerField(default=5)),
                ('prerequisites', models.TextField(blank=True)),
                ('learning_objectives', models.TextField()),
                ('materials_provided', models.TextField(blank=True)),
                ('certification_awarded', models.BooleanField(default=False)),
                ('status', models.CharField(choices=[('scheduled', 'Programmée'), ('in_progress', 'En cours'), ('completed', 'Terminée'), ('cancelled', 'Annulée')], default='scheduled', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('equipment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='trainings', to='lab_equipment.equipment')),
                ('instructor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='equipment_trainings_given', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-scheduled_date'],
            },
        ),
        migrations.CreateModel(
            name='EquipmentUsageLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_time', models.DateTimeField()),
                ('end_time', models.DateTimeField(blank=True, null=True)),
                ('duration_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('purpose', models.TextField()),
                ('materials_used', models.TextField(blank=True)),
                ('output_produced', models.TextField(blank=True)),
                ('power_consumption_kwh', models.DecimalField(blank=True, decimal_places=3, max_digits=8, null=True)),
                ('material_waste_kg', models.DecimalField(blank=True, decimal_places=2, max_digits=6, null=True)),
                ('equipment_condition_after', models.CharField(choices=[('excellent', 'Excellent'), ('good', 'Bon'), ('fair', 'Correct'), ('poor', 'Mauvais'), ('broken', 'Cassé')], default='good', max_length=20)),
                ('issues_reported', models.TextField(blank=True)),
                ('maintenance_needed', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('equipment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='usage_logs', to='lab_equipment.equipment')),
                ('reservation', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='lab_equipment.equipmentreservation')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-start_time'],
            },
        ),
        migrations.CreateModel(
            name='MaintenanceLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('maintenance_type', models.CharField(choices=[('preventive', 'Maintenance préventive'), ('corrective', 'Maintenance corrective'), ('calibration', 'Calibration'), ('upgrade', 'Mise à niveau'), ('repair', 'Réparation'), ('inspection', 'Inspection'), ('cleaning', 'Nettoyage')], max_length=20)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('scheduled_date', models.DateTimeField()),
                ('estimated_duration_hours', models.DecimalField(decimal_places=2, max_digits=5)),
                ('priority', models.CharField(choices=[('low', 'Basse'), ('normal', 'Normale'), ('high', 'Haute'), ('critical', 'Critique')], default='normal', max_length=20)),
                ('actual_start_time', models.DateTimeField(blank=True, null=True)),
                ('actual_end_time', models.DateTimeField(blank=True, null=True)),
                ('actual_duration_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('work_performed', models.TextField(blank=True, help_text='Travail effectué en détail')),
                ('parts_replaced', models.TextField(blank=True, help_text='Pièces remplacées')),
                ('tools_used', models.TextField(blank=True, help_text='Outils utilisés')),
                ('labor_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('parts_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('total_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('status', models.CharField(choices=[('scheduled', 'Programmée'), ('in_progress', 'En cours'), ('completed', 'Terminée'), ('cancelled', 'Annulée'), ('failed', 'Échec')], default='scheduled', max_length=20)),
                ('issues_found', models.TextField(blank=True, help_text='Problèmes découverts')),
                ('recommendations', models.TextField(blank=True, help_text="Recommandations pour l'avenir")),
                ('next_maintenance_due', models.DateField(blank=True, null=True)),
                ('before_photos', models.JSONField(blank=True, default=list)),
                ('after_photos', models.JSONField(blank=True, default=list)),
                ('maintenance_report', models.FileField(blank=True, null=True, upload_to='maintenance_reports/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('equipment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='maintenance_logs', to='lab_equipment.equipment')),
                ('performed_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='maintenance_performed', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-scheduled_date'],
            },
        ),
        migrations.CreateModel(
            name='UserEquipmentCertification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('level', models.CharField(choices=[('basic', 'Utilisateur de base'), ('intermediate', 'Utilisateur intermédiaire'), ('advanced', 'Utilisateur avancé'), ('instructor', 'Instructeur'), ('maintainer', 'Mainteneur')], max_length=20)),
                ('certification_date', models.DateTimeField()),
                ('expiry_date', models.DateTimeField(blank=True, null=True)),
                ('practical_score', models.IntegerField(help_text='Score pratique (0-100)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('theory_score', models.IntegerField(help_text='Score théorique (0-100)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('is_active', models.BooleanField(default=True)),
                ('notes', models.TextField(blank=True)),
                ('certified_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='certifications_given', to=settings.AUTH_USER_MODEL)),
                ('equipment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='lab_equipment.equipment')),
                ('training', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='lab_equipment.equipmenttraining')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-certification_date'],
                'unique_together': {('user', 'equipment', 'level')},
            },
        ),
    ]
