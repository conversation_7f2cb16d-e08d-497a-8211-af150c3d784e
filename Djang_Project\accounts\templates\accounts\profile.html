{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}Mon Profil - {{ block.super }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                {% if user.profile_picture %}
                    <img src="{{ user.profile_picture.url }}" alt="Photo de profil" class="rounded-circle img-fluid mb-3" style="max-width: 200px;">
                {% else %}
                    <img src="https://via.placeholder.com/200" alt="Photo de profil par défaut" class="rounded-circle img-fluid mb-3">
                {% endif %}
                <h3>{{ user.get_full_name|default:user.username }}</h3>
                <p class="text-muted">{{ user.get_role_display }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4>Modifier mon profil</h4>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-6">
                            {{ user_form|crispy }}
                        </div>
                        <div class="col-md-6">
                            {{ profile_form|crispy }}
                        </div>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">Mettre à jour</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
