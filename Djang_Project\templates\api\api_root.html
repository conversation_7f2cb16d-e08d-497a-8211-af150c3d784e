{% extends "base.html" %}
{% load static %}

{% block title %}API Documentation | {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .api-section {
        margin-bottom: 2rem;
        padding: 1.5rem;
        border-radius: 0.5rem;
        background: #fff;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .endpoint {
        border-left: 4px solid #3498db;
        padding: 1rem;
        margin: 1rem 0;
        background: #f8f9fa;
    }
    .method {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-weight: bold;
        margin-right: 0.5rem;
    }
    .get { background: #28a745; color: white; }
    .post { background: #007bff; color: white; }
    .put { background: #ffc107; color: black; }
    .delete { background: #dc3545; color: white; }
    .patch { background: #6c757d; color: white; }
    .endpoint-url {
        font-family: monospace;
        padding: 0.25rem;
        background: #e9ecef;
        border-radius: 0.25rem;
    }
    .params-table {
        width: 100%;
        margin-top: 1rem;
    }
    .params-table th {
        background: #f1f3f5;
    }
    .response-example {
        background: #272822;
        color: #f8f8f2;
        padding: 1rem;
        border-radius: 0.25rem;
        margin-top: 1rem;
        font-family: monospace;
    }
    .api-version {
        font-size: 0.9rem;
        color: #6c757d;
    }
    .authentication-info {
        background: #fff3cd;
        border: 1px solid #ffeeba;
        padding: 1rem;
        border-radius: 0.25rem;
        margin-bottom: 1.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <h1 class="mb-4">API Documentation <span class="api-version">v1.0</span></h1>
    
    <!-- Authentication Section -->
    <div class="authentication-info">
        <h5><i class="fas fa-lock"></i> Authentication</h5>
        <p class="mb-0">All API endpoints require authentication via JWT token. Include the token in the Authorization header:</p>
        <code>Authorization: Bearer &lt;your_token&gt;</code>
    </div>

    <!-- Organizations API -->
    <div class="api-section">
        <h2>Organizations</h2>
        
        <!-- List Organizations -->
        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="endpoint-url">/api/organizations/</span>
            <p>Retrieve a list of all organizations</p>
            
            <h6>Query Parameters:</h6>
            <table class="table params-table">
                <thead>
                    <tr>
                        <th>Parameter</th>
                        <th>Type</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>page</td>
                        <td>integer</td>
                        <td>Page number for pagination</td>
                    </tr>
                    <tr>
                        <td>size</td>
                        <td>integer</td>
                        <td>Number of items per page</td>
                    </tr>
                </tbody>
            </table>
            
            <div class="response-example">
                <pre>{
    "count": 100,
    "next": "http://api.example.org/organizations/?page=2",
    "previous": null,
    "results": [
        {
            "id": 1,
            "name": "Organization Name",
            "description": "Organization Description",
            "website": "https://example.org",
            "created_at": "2024-01-01T00:00:00Z"
        }
    ]
}</pre>
            </div>
        </div>

        <!-- Organization Details -->
        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="endpoint-url">/api/organizations/{id}/</span>
            <p>Retrieve details of a specific organization</p>
            
            <div class="response-example">
                <pre>{
    "id": 1,
    "name": "Organization Name",
    "description": "Organization Description",
    "website": "https://example.org",
    "created_at": "2024-01-01T00:00:00Z",
    "metrics": {
        "total_project_calls": 10,
        "active_project_calls": 5,
        "total_competitions": 8,
        "active_competitions": 3
    }
}</pre>
            </div>
        </div>
    </div>

    <!-- Project Calls API -->
    <div class="api-section">
        <h2>Project Calls</h2>
        
        <!-- List Project Calls -->
        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="endpoint-url">/api/project-calls/</span>
            <p>Retrieve a list of all project calls</p>
            
            <h6>Query Parameters:</h6>
            <table class="table params-table">
                <thead>
                    <tr>
                        <th>Parameter</th>
                        <th>Type</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>status</td>
                        <td>string</td>
                        <td>Filter by status (draft, published, closed, cancelled)</td>
                    </tr>
                    <tr>
                        <td>organization</td>
                        <td>integer</td>
                        <td>Filter by organization ID</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Create Project Call -->
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="endpoint-url">/api/project-calls/</span>
            <p>Create a new project call</p>
            
            <h6>Request Body:</h6>
            <div class="response-example">
                <pre>{
    "title": "Project Call Title",
    "description": "Project Call Description",
    "start_date": "2024-01-01",
    "end_date": "2024-12-31",
    "budget": 1000000,
    "requirements": "Project requirements..."
}</pre>
            </div>
        </div>
    </div>

    <!-- Competitions API -->
    <div class="api-section">
        <h2>Competitions</h2>
        
        <!-- List Competitions -->
        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="endpoint-url">/api/competitions/</span>
            <p>Retrieve a list of all competitions</p>
            
            <h6>Query Parameters:</h6>
            <table class="table params-table">
                <thead>
                    <tr>
                        <th>Parameter</th>
                        <th>Type</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>type</td>
                        <td>string</td>
                        <td>Filter by type (pitch, hackathon, challenge)</td>
                    </tr>
                    <tr>
                        <td>status</td>
                        <td>string</td>
                        <td>Filter by status (upcoming, ongoing, completed, cancelled)</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Competition Registration -->
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="endpoint-url">/api/competitions/{id}/register/</span>
            <p>Register a startup for a competition</p>
            
            <h6>Request Body:</h6>
            <div class="response-example">
                <pre>{
    "startup_id": 1,
    "team_members": ["John Doe", "Jane Smith"],
    "additional_info": "Additional registration information..."
}</pre>
            </div>
        </div>
    </div>

    <!-- Analytics API -->
    <div class="api-section">
        <h2>Analytics</h2>
        
        <!-- Organization Metrics -->
        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="endpoint-url">/api/analytics/organization/{id}/metrics/</span>
            <p>Retrieve analytics metrics for an organization</p>
            
            <h6>Query Parameters:</h6>
            <table class="table params-table">
                <thead>
                    <tr>
                        <th>Parameter</th>
                        <th>Type</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>date_range</td>
                        <td>string</td>
                        <td>Time period for metrics (7d, 30d, 90d, 1y)</td>
                    </tr>
                </tbody>
            </table>
            
            <div class="response-example">
                <pre>{
    "participation_rate": 75.5,
    "acceptance_rate": 45.2,
    "average_score": 4.2,
    "total_submissions": 150,
    "active_projects": 25,
    "trends": {
        "participation": 5.2,
        "acceptance": -2.1,
        "score": 0.3
    }
}</pre>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add copy functionality for endpoint URLs
    document.querySelectorAll('.endpoint-url').forEach(element => {
        element.addEventListener('click', function() {
            const text = this.textContent;
            navigator.clipboard.writeText(text).then(() => {
                const originalText = this.textContent;
                this.textContent = 'Copied!';
                setTimeout(() => {
                    this.textContent = originalText;
                }, 1000);
            });
        });
    });
</script>
{% endblock %}
