{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}Rejoindre {{ organization.name }} - Community Lab{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{% url 'organizations:organization_list' %}">Organisations</a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{% url 'organizations:organization_detail' organization.id %}">
                        {{ organization.name }}
                    </a>
                </li>
                <li class="breadcrumb-item active">Demande d'adhésion</li>
            </ol>
        </nav>

        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center mb-4">
                    {% if organization.logo %}
                        <img src="{{ organization.logo.url }}" class="rounded-circle me-3" 
                             style="width: 64px; height: 64px;" alt="{{ organization.name }}">
                    {% endif %}
                    <div>
                        <h1 class="card-title h3 mb-1">Rejoindre {{ organization.name }}</h1>
                        <p class="text-muted mb-0">{{ organization.get_type_display }}</p>
                    </div>
                </div>

                <div class="alert alert-info mb-4">
                    <h5 class="alert-heading">
                        <i class="fas fa-info-circle"></i> Important
                    </h5>
                    <p class="mb-0">
                        Votre demande sera examinée par les administrateurs de l'organisation. 
                        Nous vous notifierons dès qu'une décision sera prise.
                    </p>
                </div>

                <form method="post" class="join-request-form" novalidate>
                    {% csrf_token %}

                    <div class="mb-4">
                        {{ form.motivation|crispy }}
                    </div>

                    <div class="mb-4">
                        {{ form.experience|crispy }}
                    </div>

                    <div class="mb-4">
                        {{ form.contribution|crispy }}
                    </div>

                    <div class="mb-4">
                        {{ form.availability|crispy }}
                    </div>

                    <div class="form-check mb-4">
                        {{ form.agree_terms|crispy }}
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{% url 'organizations:organization_detail' organization.id %}" 
                           class="btn btn-outline-secondary">
                            Annuler
                        </a>
                        <button type="submit" class="btn btn-primary">
                            Envoyer la demande
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<style>
    .join-request-form .form-group {
        margin-bottom: 1rem;
    }
    .join-request-form label {
        font-weight: 500;
    }
    .join-request-form .asteriskField {
        color: #dc3545;
        margin-left: 2px;
    }
    .join-request-form .help-text {
        font-size: 0.875rem;
        color: #6c757d;
    }
    .join-request-form textarea {
        min-height: 120px;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.querySelector('.join-request-form');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });

    // Character counter for textareas
    const textareas = document.querySelectorAll('textarea');
    textareas.forEach(textarea => {
        const maxLength = textarea.getAttribute('maxlength');
        if (maxLength) {
            const counter = document.createElement('small');
            counter.classList.add('text-muted', 'd-block', 'text-end');
            textarea.parentNode.appendChild(counter);

            function updateCounter() {
                const remaining = maxLength - textarea.value.length;
                counter.textContent = `${remaining} caractères restants`;
            }

            textarea.addEventListener('input', updateCounter);
            updateCounter();
        }
    });

    // Terms agreement validation
    const agreeTerms = document.querySelector('input[name="agree_terms"]');
    const submitButton = document.querySelector('button[type="submit"]');
    
    if (agreeTerms && submitButton) {
        function updateSubmitButton() {
            submitButton.disabled = !agreeTerms.checked;
        }
        
        agreeTerms.addEventListener('change', updateSubmitButton);
        updateSubmitButton();
    }
});
</script>
{% endblock %}
