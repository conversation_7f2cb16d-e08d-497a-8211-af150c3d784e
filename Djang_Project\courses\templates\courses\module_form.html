{% extends "base.html" %}
{% load crispy_forms_tags %}

{% block title %}
    {% if form.instance.pk %}
        Modifier le module
    {% else %}
        Créer un nouveau module
    {% endif %}
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        {% if form.instance.pk %}
                            Modifier le module
                        {% else %}
                            Créer un nouveau module
                        {% endif %}
                    </h4>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        {{ form|crispy }}
                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">
                                {% if form.instance.pk %}
                                    Mettre à jour
                                {% else %}
                                    <PERSON><PERSON>er
                                {% endif %}
                            </button>
                            <a href="{% url 'courses:course_detail' slug=view.kwargs.slug %}" class="btn btn-secondary">
                                Annuler
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
