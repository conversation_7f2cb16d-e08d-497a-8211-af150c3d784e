# Generated by Django 4.2.17 on 2025-01-02 20:33

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('contenttypes', '0002_remove_content_type_name'),
        ('education', '0003_alter_quiz_options_remove_quiz_time_limit_minutes_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='MediaCollection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True)),
                ('object_id', models.PositiveIntegerField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('content_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_collections', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='MediaResource',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True)),
                ('resource_type', models.CharField(choices=[('video', 'Video'), ('document', 'Document'), ('image', 'Image'), ('audio', 'Audio')], max_length=20)),
                ('file', models.FileField(blank=True, null=True, upload_to='media_resources/')),
                ('url', models.URLField(blank=True, null=True)),
                ('thumbnail', models.ImageField(blank=True, null=True, upload_to='thumbnails/')),
                ('video_provider', models.CharField(blank=True, max_length=50, null=True)),
                ('video_id', models.CharField(blank=True, max_length=100, null=True)),
                ('embed_url', models.URLField(blank=True, null=True)),
                ('mime_type', models.CharField(blank=True, max_length=100, null=True)),
                ('preview_url', models.URLField(blank=True, null=True)),
                ('object_id', models.PositiveIntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_media', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='MediaCollectionItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order', models.PositiveIntegerField(default=0)),
                ('collection', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='education.mediacollection')),
                ('resource', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='education.mediaresource')),
            ],
            options={
                'ordering': ['order'],
            },
        ),
        migrations.AddField(
            model_name='mediacollection',
            name='resources',
            field=models.ManyToManyField(through='education.MediaCollectionItem', to='education.mediaresource'),
        ),
        migrations.AddIndex(
            model_name='mediaresource',
            index=models.Index(fields=['resource_type'], name='education_m_resourc_dee069_idx'),
        ),
        migrations.AddIndex(
            model_name='mediaresource',
            index=models.Index(fields=['content_type', 'object_id'], name='education_m_content_04cfdf_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='mediacollectionitem',
            unique_together={('collection', 'resource')},
        ),
    ]
