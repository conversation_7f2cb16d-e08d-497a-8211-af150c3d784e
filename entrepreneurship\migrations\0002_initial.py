# Generated by Django 4.2.8 on 2024-12-30 07:00

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('users', '0001_initial'),
        ('entrepreneurship', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='startup',
            name='entrepreneur',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='startups', to='users.entrepreneur'),
        ),
        migrations.AddField(
            model_name='startup',
            name='mentors',
            field=models.ManyToManyField(blank=True, related_name='mentored_startups', to='users.mentor'),
        ),
        migrations.AddField(
            model_name='resource',
            name='project',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='resources', to='entrepreneurship.project'),
        ),
        migrations.AddField(
            model_name='project',
            name='startup',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='projects', to='entrepreneurship.startup'),
        ),
        migrations.AddField(
            model_name='milestone',
            name='project',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='milestones', to='entrepreneurship.project'),
        ),
    ]
