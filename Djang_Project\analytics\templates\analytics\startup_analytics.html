{% extends 'base.html' %}

{% block title %}Analytiques des startups - Community Lab{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-md-12">
            <h1>Analytiques des startups</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'analytics:dashboard' %}">Tableau de bord</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Startups</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row">
        {% for metric in metrics %}
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ metric.startup.name }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <h6 class="text-muted">Projets totaux</h6>
                                <h2>{{ metric.total_projects }}</h2>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <h6 class="text-muted">Projets actifs</h6>
                                <h2>{{ metric.active_projects }}</h2>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <h6 class="text-muted">Taille équipe</h6>
                                <h2>{{ metric.team_size }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h6>Ratio projets actifs/totaux</h6>
                        <div class="progress" style="height: 10px;">
                            {% if metric.total_projects > 0 %}
                            <div class="progress-bar bg-success" role="progressbar" 
                                 style="width: {% widthratio metric.active_projects metric.total_projects 100 %}%"
                                 aria-valuenow="{% widthratio metric.active_projects metric.total_projects 100 %}" 
                                 aria-valuemin="0" aria-valuemax="100">
                            </div>
                            {% else %}
                            <div class="progress-bar" role="progressbar" style="width: 0%"
                                 aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">Dernière mise à jour : {{ metric.last_updated|date:"d/m/Y H:i" }}</small>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{% url 'startups:startup_detail' metric.startup.id %}" class="btn btn-sm btn-primary">
                        <i class="fas fa-eye"></i> Voir la startup
                    </a>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="alert alert-info">
                Aucune donnée analytique disponible pour le moment.
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}
