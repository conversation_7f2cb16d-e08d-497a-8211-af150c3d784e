{% extends 'base.html' %}

{% block title %}Cours - Community Lab{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1>Cours disponibles</h1>
    </div>
    {% if user.is_authenticated and user.is_mentor %}
    <div class="col-md-4 text-end">
        <a href="{% url 'learning:course_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Créer un cours
        </a>
    </div>
    {% endif %}
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <form method="get" class="card p-3">
            <div class="row">
                <div class="col-md-4">
                    <input type="text" name="search" class="form-control" placeholder="Rechercher un cours..." 
                           value="{{ request.GET.search }}">
                </div>
                <div class="col-md-3">
                    <select name="category" class="form-select">
                        <option value="">Toutes les catégories</option>
                        {% for category in categories %}
                            <option value="{{ category.id }}" {% if request.GET.category == category.id|stringformat:"s" %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <select name="level" class="form-select">
                        <option value="">Tous les niveaux</option>
                        <option value="BEGINNER" {% if request.GET.level == 'BEGINNER' %}selected{% endif %}>Débutant</option>
                        <option value="INTERMEDIATE" {% if request.GET.level == 'INTERMEDIATE' %}selected{% endif %}>Intermédiaire</option>
                        <option value="ADVANCED" {% if request.GET.level == 'ADVANCED' %}selected{% endif %}>Avancé</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">Filtrer</button>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="row">
    {% if courses %}
        {% for course in courses %}
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    {% if course.image %}
                        <img src="{{ course.image.url }}" class="card-img-top" alt="{{ course.title }}">
                    {% endif %}
                    <div class="card-body">
                        <h5 class="card-title">{{ course.title }}</h5>
                        <p class="card-text">{{ course.description|truncatewords:20 }}</p>
                        <div class="mb-2">
                            <span class="badge bg-primary">{{ course.get_level_display }}</span>
                            <span class="badge bg-secondary">{{ course.category.name }}</span>
                        </div>
                        <p class="card-text">
                            <small class="text-muted">
                                Par {{ course.mentor.get_full_name|default:course.mentor.username }}
                            </small>
                        </p>
                    </div>
                    <div class="card-footer bg-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-users"></i> {{ course.enrolled_students.count }} étudiants
                            </div>
                            <a href="{% url 'learning:course_detail' course.id %}" class="btn btn-outline-primary">
                                Voir le cours
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    {% else %}
        <div class="col-12">
            <div class="alert alert-info">
                Aucun cours ne correspond à vos critères de recherche.
            </div>
        </div>
    {% endif %}
</div>

{% if is_paginated %}
<nav aria-label="Navigation des pages">
    <ul class="pagination justify-content-center">
        {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.level %}&level={{ request.GET.level }}{% endif %}">Précédent</a>
            </li>
        {% endif %}

        {% for num in page_obj.paginator.page_range %}
            {% if page_obj.number == num %}
                <li class="page-item active">
                    <span class="page-link">{{ num }}</span>
                </li>
            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ num }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.level %}&level={{ request.GET.level }}{% endif %}">{{ num }}</a>
                </li>
            {% endif %}
        {% endfor %}

        {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.level %}&level={{ request.GET.level }}{% endif %}">Suivant</a>
            </li>
        {% endif %}
    </ul>
</nav>
{% endif %}
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
{% endblock %}
