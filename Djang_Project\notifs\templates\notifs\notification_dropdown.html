{% load static %}

<div class="dropdown">
    <button class="btn btn-link nav-link position-relative" type="button" id="notificationDropdown" 
            data-bs-toggle="dropdown" aria-expanded="false">
        <i class="fas fa-bell"></i>
        {% if unread_count > 0 %}
            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                {{ unread_count }}
                <span class="visually-hidden">notifications non lues</span>
            </span>
        {% endif %}
    </button>
    <div class="dropdown-menu dropdown-menu-end notification-dropdown" aria-labelledby="notificationDropdown"
         style="width: 350px; max-height: 500px; overflow-y: auto;">
        <div class="d-flex justify-content-between align-items-center px-3 py-2 border-bottom">
            <h6 class="mb-0">Notifications</h6>
            {% if unread_count > 0 %}
                <form action="{% url 'notifs:mark_all_as_read' %}" method="post" class="d-inline">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-link btn-sm text-decoration-none">
                        <i class="fas fa-check-double"></i> Tout marquer comme lu
                    </button>
                </form>
            {% endif %}
        </div>
        
        {% if notifications %}
            <div class="notifications-list">
                {% for notification in notifications|slice:":5" %}
                    <div class="dropdown-item {% if not notification.is_read %}bg-light{% endif %} border-bottom">
                        <div class="d-flex align-items-start">
                            <div class="me-2">
                                {% if notification.level == 'success' %}
                                    <i class="fas fa-check-circle text-success"></i>
                                {% elif notification.level == 'warning' %}
                                    <i class="fas fa-exclamation-triangle text-warning"></i>
                                {% elif notification.level == 'error' %}
                                    <i class="fas fa-times-circle text-danger"></i>
                                {% else %}
                                    <i class="fas fa-info-circle text-info"></i>
                                {% endif %}
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-0">{{ notification.title }}</h6>
                                <p class="mb-1 small">{{ notification.message|truncatechars:100 }}</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">{{ notification.created_at|timesince }}</small>
                                    {% if notification.action_url %}
                                        <a href="{{ notification.action_url }}" class="btn btn-sm btn-primary">
                                            {{ notification.action_text }}
                                        </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
            
            <div class="text-center p-2 border-top">
                <a href="{% url 'notifs:notification_list' %}" class="text-decoration-none">
                    Voir toutes les notifications
                </a>
            </div>
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-bell-slash fa-2x text-muted mb-2"></i>
                <p class="text-muted mb-0">Aucune notification</p>
            </div>
        {% endif %}
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const dropdown = document.querySelector('.notification-dropdown');
    
    // Empêcher la fermeture du dropdown lors du clic sur les formulaires
    if (dropdown) {
        dropdown.addEventListener('click', function(e) {
            if (e.target.closest('form')) {
                e.stopPropagation();
            }
        });

        // Gestion des formulaires de marquage comme lu
        dropdown.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                fetch(this.action, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': this.querySelector('[name=csrfmiddlewaretoken]').value,
                    },
                    credentials: 'same-origin'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        location.reload();
                    }
                })
                .catch(error => console.error('Error:', error));
            });
        });
    }
});
