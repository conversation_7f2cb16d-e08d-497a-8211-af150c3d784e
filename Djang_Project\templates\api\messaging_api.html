{% extends "base.html" %}
{% load static %}

{% block title %}Messaging & Chat API Documentation | {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .api-section {
        margin-bottom: 2rem;
        padding: 1.5rem;
        border-radius: 0.5rem;
        background: #fff;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .endpoint {
        border-left: 4px solid #3498db;
        padding: 1rem;
        margin: 1rem 0;
        background: #f8f9fa;
    }
    .method {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-weight: bold;
        margin-right: 0.5rem;
    }
    .get { background: #28a745; color: white; }
    .post { background: #007bff; color: white; }
    .put { background: #ffc107; color: black; }
    .delete { background: #dc3545; color: white; }
    .patch { background: #6c757d; color: white; }
    .endpoint-url {
        font-family: monospace;
        padding: 0.25rem;
        background: #e9ecef;
        border-radius: 0.25rem;
        cursor: pointer;
    }
    .message-note {
        background: #e8f4f8;
        border: 1px solid #bee5eb;
        padding: 1rem;
        border-radius: 0.25rem;
        margin-bottom: 1.5rem;
    }
    .response-example {
        background: #272822;
        color: #f8f8f2;
        padding: 1rem;
        border-radius: 0.25rem;
        margin-top: 1rem;
        font-family: monospace;
    }
    .message-type {
        display: inline-block;
        padding: 0.2rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.8rem;
        margin-right: 0.5rem;
    }
    .direct { background: #007bff; color: white; }
    .group { background: #28a745; color: white; }
    .broadcast { background: #dc3545; color: white; }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <h1 class="mb-4">Messaging & Chat API Documentation</h1>

    <div class="message-note">
        <h5><i class="fas fa-comments"></i> Message Types</h5>
        <p class="mb-0">
            Available types:
            <span class="message-type direct">Message Direct</span>
            <span class="message-type group">Message Groupe</span>
            <span class="message-type broadcast">Diffusion</span>
        </p>
    </div>

    <!-- Direct Messaging -->
    <div class="api-section">
        <h2>Direct Messaging</h2>

        <!-- Send Message -->
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="endpoint-url">/api/messages/direct/</span>
            <p>Send a direct message</p>

            <h6>Request Body:</h6>
            <div class="response-example">
                <pre>{
    "recipient_id": "user-123",
    "content": "Bonjour! Comment allez-vous?",
    "attachments": [
        {
            "file_id": "file-789",
            "type": "image"
        }
    ],
    "metadata": {
        "reply_to": "message-456",
        "mentions": ["user-789"]
    }
}</pre>
            </div>

            <h6>Response:</h6>
            <div class="response-example">
                <pre>{
    "id": "message-123",
    "sender": {
        "id": "user-456",
        "name": "Alice Smith",
        "avatar_url": "https://example.com/avatars/alice.jpg"
    },
    "recipient": {
        "id": "user-123",
        "name": "Bob Johnson"
    },
    "content": "Bonjour! Comment allez-vous?",
    "sent_at": "2024-01-19T14:00:00Z",
    "status": "sent"
}</pre>
            </div>
        </div>

        <!-- List Conversations -->
        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="endpoint-url">/api/messages/conversations/</span>
            <p>List user conversations</p>

            <h6>Query Parameters:</h6>
            <table class="table params-table">
                <thead>
                    <tr>
                        <th>Parameter</th>
                        <th>Type</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>status</td>
                        <td>string</td>
                        <td>Filter by status (active, archived)</td>
                    </tr>
                    <tr>
                        <td>unread</td>
                        <td>boolean</td>
                        <td>Filter unread conversations</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Group Chat -->
    <div class="api-section">
        <h2>Group Chat</h2>

        <!-- Create Group -->
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="endpoint-url">/api/messages/groups/</span>
            <p>Create a group chat</p>

            <h6>Request Body:</h6>
            <div class="response-example">
                <pre>{
    "name": "Équipe Technique",
    "description": "Groupe pour l'équipe technique",
    "members": ["user-123", "user-456", "user-789"],
    "avatar": "file-123",
    "settings": {
        "allow_member_invites": true,
        "message_retention_days": 30
    }
}</pre>
            </div>
        </div>

        <!-- Send Group Message -->
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="endpoint-url">/api/messages/groups/{group_id}/messages/</span>
            <p>Send a message to a group</p>

            <h6>Request Body:</h6>
            <div class="response-example">
                <pre>{
    "content": "Réunion d'équipe demain à 10h",
    "attachments": [
        {
            "file_id": "file-789",
            "type": "document"
        }
    ],
    "announcement": false
}</pre>
            </div>
        </div>
    </div>

    <!-- Broadcast Messages -->
    <div class="api-section">
        <h2>Broadcast Messages</h2>

        <!-- Send Broadcast -->
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="endpoint-url">/api/messages/broadcast/</span>
            <p>Send a broadcast message</p>

            <h6>Request Body:</h6>
            <div class="response-example">
                <pre>{
    "title": "Maintenance Planifiée",
    "content": "Le système sera en maintenance le 20 janvier",
    "target": {
        "roles": ["admin", "manager"],
        "departments": ["tech", "operations"],
        "locations": ["Bujumbura"]
    },
    "schedule": {
        "send_at": "2024-01-19T18:00:00Z",
        "expire_at": "2024-01-21T00:00:00Z"
    },
    "options": {
        "require_acknowledgment": true,
        "priority": "high"
    }
}</pre>
            </div>
        </div>

        <!-- List Broadcasts -->
        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="endpoint-url">/api/messages/broadcasts/</span>
            <p>List broadcast messages</p>
        </div>
    </div>

    <!-- Message Actions -->
    <div class="api-section">
        <h2>Message Actions</h2>

        <!-- React to Message -->
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="endpoint-url">/api/messages/{message_id}/reactions/</span>
            <p>Add reaction to message</p>

            <h6>Request Body:</h6>
            <div class="response-example">
                <pre>{
    "emoji": "👍",
    "type": "like"
}</pre>
            </div>
        </div>

        <!-- Forward Message -->
        <div class="endpoint">
            <span class="method post">POST</span>
            <span class="endpoint-url">/api/messages/{message_id}/forward/</span>
            <p>Forward a message</p>

            <h6>Request Body:</h6>
            <div class="response-example">
                <pre>{
    "recipients": ["user-123", "group-456"],
    "note": "Information importante"
}</pre>
            </div>
        </div>
    </div>

    <!-- Message Search -->
    <div class="api-section">
        <h2>Message Search</h2>

        <!-- Search Messages -->
        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="endpoint-url">/api/messages/search/</span>
            <p>Search messages</p>

            <h6>Query Parameters:</h6>
            <table class="table params-table">
                <thead>
                    <tr>
                        <th>Parameter</th>
                        <th>Type</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>q</td>
                        <td>string</td>
                        <td>Search query</td>
                    </tr>
                    <tr>
                        <td>type</td>
                        <td>string</td>
                        <td>Message type</td>
                    </tr>
                    <tr>
                        <td>date_range</td>
                        <td>string</td>
                        <td>Date range</td>
                    </tr>
                </tbody>
            </table>

            <h6>Response:</h6>
            <div class="response-example">
                <pre>{
    "results": [
        {
            "id": "message-123",
            "type": "direct",
            "content": "Bonjour! Comment allez-vous?",
            "sender": {
                "id": "user-456",
                "name": "Alice Smith"
            },
            "sent_at": "2024-01-19T14:00:00Z",
            "context": {
                "conversation_id": "conv-789",
                "highlight": "<em>Bonjour</em>! Comment allez-vous?"
            }
        }
    ],
    "total": 15,
    "page": 1,
    "per_page": 10
}</pre>
            </div>
        </div>
    </div>

    <!-- Message Analytics -->
    <div class="api-section">
        <h2>Message Analytics</h2>

        <!-- Get Analytics -->
        <div class="endpoint">
            <span class="method get">GET</span>
            <span class="endpoint-url">/api/messages/analytics/</span>
            <p>Get messaging analytics</p>

            <h6>Response:</h6>
            <div class="response-example">
                <pre>{
    "overview": {
        "total_messages": 1500,
        "active_conversations": 45,
        "average_response_time": 300  // seconds
    },
    "by_type": {
        "direct": 800,
        "group": 600,
        "broadcast": 100
    },
    "engagement": {
        "reactions_per_message": 2.5,
        "replies_per_thread": 4.2
    },
    "peak_hours": [
        {
            "hour": 9,
            "message_count": 250
        },
        {
            "hour": 14,
            "message_count": 300
        }
    ]
}</pre>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add copy functionality for endpoint URLs
    document.querySelectorAll('.endpoint-url').forEach(element => {
        element.addEventListener('click', function() {
            const text = this.textContent;
            navigator.clipboard.writeText(text).then(() => {
                const originalText = this.textContent;
                this.textContent = 'Copied!';
                setTimeout(() => {
                    this.textContent = originalText;
                }, 1000);
            });
        });
    });
</script>
{% endblock %}
