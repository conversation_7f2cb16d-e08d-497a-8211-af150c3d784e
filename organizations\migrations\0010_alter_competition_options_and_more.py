# Generated by Django 4.2.17 on 2025-01-02 20:33

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('entrepreneurship', '0003_alter_milestone_options_alter_project_options_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('organizations', '0009_add_registration_deadline'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='competition',
            options={'ordering': ['-start_date']},
        ),
        migrations.RemoveField(
            model_name='competition',
            name='prize_pool',
        ),
        migrations.AddField(
            model_name='competition',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_competitions', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='competition',
            name='description',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='competition',
            name='end_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='competition',
            name='organization',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='competitions', to='organizations.organization'),
        ),
        migrations.AlterField(
            model_name='competition',
            name='registration_deadline',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='competition',
            name='rules',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='competition',
            name='start_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='competition',
            name='status',
            field=models.CharField(choices=[('draft', 'Draft'), ('published', 'Published'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='draft', max_length=20),
        ),
        migrations.AlterField(
            model_name='competition',
            name='title',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='organization',
            name='type',
            field=models.CharField(blank=True, choices=[('startup', 'Startup'), ('company', 'Company'), ('university', 'University'), ('ngo', 'NGO'), ('government', 'Government'), ('other', 'Other')], max_length=20, null=True),
        ),
        migrations.CreateModel(
            name='EvaluationCriteria',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('weight', models.DecimalField(decimal_places=2, help_text='Percentage weight in final score', max_digits=5)),
                ('max_score', models.IntegerField(default=10)),
                ('order', models.PositiveIntegerField()),
                ('competition', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='evaluation_criteria', to='organizations.competition')),
            ],
            options={
                'ordering': ['order'],
                'unique_together': {('competition', 'order')},
            },
        ),
        migrations.CreateModel(
            name='CompetitionParticipant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('registered', 'Registered'), ('in_progress', 'In Progress'), ('submitted', 'Submitted'), ('evaluated', 'Evaluated'), ('winner', 'Winner'), ('eliminated', 'Eliminated')], default='registered', max_length=20)),
                ('registration_date', models.DateTimeField(auto_now_add=True)),
                ('submission_date', models.DateTimeField(blank=True, null=True)),
                ('final_score', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('feedback', models.TextField(blank=True)),
                ('rank', models.PositiveIntegerField(blank=True, null=True)),
                ('competition', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='participants', to='organizations.competition')),
                ('participant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='competition_participations', to=settings.AUTH_USER_MODEL)),
                ('startup', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='competition_participations', to='entrepreneurship.startup')),
            ],
            options={
                'ordering': ['rank', 'final_score'],
                'unique_together': {('competition', 'participant')},
            },
        ),
        migrations.CreateModel(
            name='CompetitionJudge',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('expertise', models.CharField(max_length=100)),
                ('assigned_categories', models.TextField(help_text='Categories or aspects to evaluate')),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('competition', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='judges', to='organizations.competition')),
                ('judge', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='judged_competitions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('competition', 'judge')},
            },
        ),
        migrations.CreateModel(
            name='Evaluation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('score', models.DecimalField(decimal_places=2, max_digits=5)),
                ('comments', models.TextField(blank=True)),
                ('evaluated_at', models.DateTimeField(auto_now=True)),
                ('criteria', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='evaluations', to='organizations.evaluationcriteria')),
                ('judge', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='evaluations', to='organizations.competitionjudge')),
                ('participant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='evaluations', to='organizations.competitionparticipant')),
            ],
            options={
                'unique_together': {('participant', 'judge', 'criteria')},
            },
        ),
    ]
