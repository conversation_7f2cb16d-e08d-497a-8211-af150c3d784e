# Dépendances de développement pour Community Laboratory Burundi

# Testing
pytest==7.4.3
pytest-django==4.7.0
pytest-cov==4.1.0
factory-boy==3.3.0
faker==20.1.0

# Code Quality
flake8==6.1.0
black==23.11.0
isort==5.12.0
mypy==1.7.1
django-stubs==4.2.7

# Documentation
sphinx==7.2.6
sphinx-rtd-theme==1.3.0
mkdocs==1.5.3
mkdocs-material==9.4.8

# Development Tools
django-debug-toolbar==4.2.0
django-extensions==3.2.3
ipython==8.17.2
jupyter==1.0.0

# API Testing
requests==2.31.0
httpx==0.25.2

# Performance
django-silk==5.0.4
memory-profiler==0.61.0

# Security
bandit==1.7.5
safety==2.3.5

# Pre-commit hooks
pre-commit==3.5.0

# Database
psycopg2-binary>=2.9.9

# Environment
python-decouple==3.8
