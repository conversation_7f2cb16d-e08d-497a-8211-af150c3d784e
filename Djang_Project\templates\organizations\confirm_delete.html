{% extends "organizations/base_organizations.html" %}

{% block title %}Confirmer la suppression | {{ block.super }}{% endblock %}

{% block breadcrumb %}
{% if object_type == 'project_call' %}
<li class="breadcrumb-item"><a href="{% url 'organizations:project_call_list' %}">Appels à Projets</a></li>
<li class="breadcrumb-item"><a href="{% url 'organizations:project_call_detail' object.pk %}">{{ object.title }}</a></li>
{% elif object_type == 'competition' %}
<li class="breadcrumb-item"><a href="{% url 'organizations:competition_list' %}">Compétitions</a></li>
<li class="breadcrumb-item"><a href="{% url 'organizations:competition_detail' object.pk %}">{{ object.title }}</a></li>
{% endif %}
<li class="breadcrumb-item active">Supprimer</li>
{% endblock %}

{% block organization_content %}
<div class="row">
    <div class="col-md-6 mx-auto">
        <div class="card">
            <div class="card-body">
                <h2 class="card-title text-danger mb-4">Confirmer la suppression</h2>
                
                <div class="alert alert-warning">
                    <h5 class="alert-heading">Attention!</h5>
                    <p class="mb-0">
                        Êtes-vous sûr de vouloir supprimer 
                        {% if object_type == 'project_call' %}
                        l'appel à projets
                        {% elif object_type == 'competition' %}
                        la compétition
                        {% endif %}
                        <strong>{{ object.title }}</strong> ?
                    </p>
                </div>

                {% if object_type == 'project_call' and object.submissions.exists %}
                <div class="alert alert-danger mb-4">
                    <h5 class="alert-heading">Données associées</h5>
                    <p class="mb-0">
                        Cet appel à projets a {{ object.submissions.count }} soumission(s).
                        La suppression entraînera également la suppression de toutes les soumissions associées.
                    </p>
                </div>
                {% elif object_type == 'competition' and object.registrations.exists %}
                <div class="alert alert-danger mb-4">
                    <h5 class="alert-heading">Données associées</h5>
                    <p class="mb-0">
                        Cette compétition a {{ object.registrations.count }} inscription(s).
                        La suppression entraînera également la suppression de toutes les inscriptions associées.
                    </p>
                </div>
                {% endif %}

                <form method="post">
                    {% csrf_token %}
                    <div class="d-flex justify-content-between">
                        {% if object_type == 'project_call' %}
                        <a href="{% url 'organizations:project_call_detail' object.pk %}" class="btn btn-outline-secondary">
                            Annuler
                        </a>
                        {% elif object_type == 'competition' %}
                        <a href="{% url 'organizations:competition_detail' object.pk %}" class="btn btn-outline-secondary">
                            Annuler
                        </a>
                        {% endif %}
                        <button type="submit" class="btn btn-danger">
                            Confirmer la suppression
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
