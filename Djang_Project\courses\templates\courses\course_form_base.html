{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs4.min.css" rel="stylesheet">
<style>
    .module-form {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .content-form {
        background-color: #fff;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
    }
    .remove-btn {
        color: #dc3545;
        cursor: pointer;
    }
    .remove-btn:hover {
        color: #bd2130;
    }
    .drag-handle {
        cursor: move;
        color: #6c757d;
    }
    .module-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{% url 'courses:course_list' %}">Cours</a>
                    </li>
                    <li class="breadcrumb-item active">
                        {% if course %}Modifier{% else %}Créer{% endif %} un cours
                    </li>
                </ol>
            </nav>

            <div class="card">
                <div class="card-body">
                    <h1 class="card-title mb-4">
                        {% if course %}Modifier{% else %}Créer{% endif %} un cours
                    </h1>

                    <form method="post" enctype="multipart/form-data" id="courseForm">
                        {% csrf_token %}
                        
                        <!-- Onglets de navigation -->
                        <ul class="nav nav-tabs mb-4" id="courseTab" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="basic-tab" data-bs-toggle="tab"
                                   href="#basic" role="tab">
                                    Informations de base
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="content-tab" data-bs-toggle="tab"
                                   href="#content" role="tab">
                                    Contenu du cours
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="settings-tab" data-bs-toggle="tab"
                                   href="#settings" role="tab">
                                    Paramètres
                                </a>
                            </li>
                        </ul>

                        <!-- Contenu des onglets -->
                        <div class="tab-content" id="courseTabContent">
                            <!-- Informations de base -->
                            <div class="tab-pane fade show active" id="basic" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-8">
                                        {{ form.title|crispy }}
                                        {{ form.overview|crispy }}
                                        {{ form.description|crispy }}
                                    </div>
                                    <div class="col-md-4">
                                        {{ form.image|crispy }}
                                        {% if course.image %}
                                        <div class="mb-3">
                                            <img src="{{ course.image.url }}" 
                                                 class="img-fluid rounded" 
                                                 alt="{{ course.title }}">
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Contenu du cours -->
                            <div class="tab-pane fade" id="content" role="tabpanel">
                                <div id="modules-container">
                                    {% for module_form in module_formset %}
                                    <div class="module-form">
                                        <div class="module-header">
                                            <i class="fas fa-grip-vertical drag-handle"></i>
                                            <i class="fas fa-times remove-btn" 
                                               onclick="removeModule(this)"></i>
                                        </div>
                                        {{ module_form|crispy }}
                                        
                                        <div class="contents-container">
                                            {% for content_form in module_form.content_formset %}
                                            <div class="content-form">
                                                <div class="d-flex justify-content-between mb-2">
                                                    <i class="fas fa-grip-vertical drag-handle"></i>
                                                    <i class="fas fa-times remove-btn" 
                                                       onclick="removeContent(this)"></i>
                                                </div>
                                                {{ content_form|crispy }}
                                            </div>
                                            {% endfor %}
                                        </div>
                                        
                                        <button type="button" 
                                                class="btn btn-outline-primary btn-sm mt-2"
                                                onclick="addContent(this)">
                                            <i class="fas fa-plus me-1"></i>
                                            Ajouter du contenu
                                        </button>
                                    </div>
                                    {% endfor %}
                                </div>

                                <button type="button" class="btn btn-outline-primary mt-3"
                                        onclick="addModule()">
                                    <i class="fas fa-plus me-1"></i>
                                    Ajouter un module
                                </button>
                            </div>

                            <!-- Paramètres -->
                            <div class="tab-pane fade" id="settings" role="tabpanel">
                                <div class="row">
                                    <div class="col-md-6">
                                        {{ form.level|crispy }}
                                        {{ form.language|crispy }}
                                        {{ form.start_date|crispy }}
                                    </div>
                                    <div class="col-md-6">
                                        {{ form.prerequisites|crispy }}
                                        {{ form.learning_objectives|crispy }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'courses:course_list' %}" 
                               class="btn btn-outline-secondary">
                                Annuler
                            </a>
                            <button type="submit" class="btn btn-primary">
                                {% if course %}Mettre à jour{% else %}Créer{% endif %} le cours
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Templates for dynamic form generation -->
<div id="module-template" style="display: none;">
    <div class="module-form">
        <div class="module-header">
            <i class="fas fa-grip-vertical drag-handle"></i>
            <i class="fas fa-times remove-btn" onclick="removeModule(this)"></i>
        </div>
        {{ empty_module_form|crispy }}
        <div class="contents-container"></div>
        <button type="button" class="btn btn-outline-primary btn-sm mt-2"
                onclick="addContent(this)">
            <i class="fas fa-plus me-1"></i>
            Ajouter du contenu
        </button>
    </div>
</div>

<div id="content-template" style="display: none;">
    <div class="content-form">
        <div class="d-flex justify-content-between mb-2">
            <i class="fas fa-grip-vertical drag-handle"></i>
            <i class="fas fa-times remove-btn" onclick="removeContent(this)"></i>
        </div>
        {{ empty_content_form|crispy }}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs4.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.14.0/Sortable.min.js"></script>
<script>
$(document).ready(function() {
    // Initialize rich text editors
    $('.rich-text').summernote({
        height: 200,
        toolbar: [
            ['style', ['style']],
            ['font', ['bold', 'italic', 'underline', 'clear']],
            ['para', ['ul', 'ol', 'paragraph']],
            ['insert', ['link']],
            ['view', ['fullscreen', 'codeview']]
        ]
    });

    // Initialize sortable modules
    new Sortable(document.getElementById('modules-container'), {
        handle: '.drag-handle',
        animation: 150
    });

    // Initialize sortable contents within each module
    document.querySelectorAll('.contents-container').forEach(container => {
        new Sortable(container, {
            handle: '.drag-handle',
            animation: 150
        });
    });

    // Show content type specific fields
    $('.content-type-select').change(function() {
        const container = $(this).closest('.content-form');
        showContentTypeFields(container);
    });
});

function addModule() {
    const template = document.getElementById('module-template');
    const container = document.getElementById('modules-container');
    const newModule = template.firstElementChild.cloneNode(true);
    
    // Update form indices
    updateFormIndices(newModule, container.children.length);
    
    container.appendChild(newModule);
    
    // Initialize sortable for new module's contents
    new Sortable(newModule.querySelector('.contents-container'), {
        handle: '.drag-handle',
        animation: 150
    });

    // Initialize rich text editors in new module
    $(newModule).find('.rich-text').summernote({
        height: 200,
        toolbar: [
            ['style', ['style']],
            ['font', ['bold', 'italic', 'underline', 'clear']],
            ['para', ['ul', 'ol', 'paragraph']],
            ['insert', ['link']],
            ['view', ['fullscreen', 'codeview']]
        ]
    });
}

function addContent(btn) {
    const template = document.getElementById('content-template');
    const container = btn.previousElementSibling;
    const newContent = template.firstElementChild.cloneNode(true);
    
    // Update form indices
    const moduleIndex = Array.from(container.closest('.module-form').parentNode.children).indexOf(container.closest('.module-form'));
    const contentIndex = container.children.length;
    updateContentFormIndices(newContent, moduleIndex, contentIndex);
    
    container.appendChild(newContent);
    
    // Initialize rich text editors in new content
    $(newContent).find('.rich-text').summernote({
        height: 200,
        toolbar: [
            ['style', ['style']],
            ['font', ['bold', 'italic', 'underline', 'clear']],
            ['para', ['ul', 'ol', 'paragraph']],
            ['insert', ['link']],
            ['view', ['fullscreen', 'codeview']]
        ]
    });

    // Initialize content type fields
    const typeSelect = newContent.querySelector('.content-type-select');
    if (typeSelect) {
        $(typeSelect).change(function() {
            showContentTypeFields($(this).closest('.content-form'));
        });
    }
}

function removeModule(btn) {
    const module = btn.closest('.module-form');
    module.remove();
    updateAllFormIndices();
}

function removeContent(btn) {
    const content = btn.closest('.content-form');
    content.remove();
    updateAllFormIndices();
}

function updateFormIndices(element, newIndex) {
    element.querySelectorAll('input, select, textarea').forEach(input => {
        const name = input.getAttribute('name');
        if (name) {
            input.setAttribute('name', name.replace(/\d+/, newIndex));
            input.setAttribute('id', input.getAttribute('id').replace(/\d+/, newIndex));
        }
    });
    element.querySelectorAll('label').forEach(label => {
        const forAttr = label.getAttribute('for');
        if (forAttr) {
            label.setAttribute('for', forAttr.replace(/\d+/, newIndex));
        }
    });
}

function updateContentFormIndices(element, moduleIndex, contentIndex) {
    element.querySelectorAll('input, select, textarea').forEach(input => {
        const name = input.getAttribute('name');
        if (name) {
            const newName = name.replace(/module_set-\d+/, `module_set-${moduleIndex}`)
                               .replace(/content_set-\d+/, `content_set-${contentIndex}`);
            input.setAttribute('name', newName);
            input.setAttribute('id', input.getAttribute('id').replace(/\d+/, contentIndex));
        }
    });
    element.querySelectorAll('label').forEach(label => {
        const forAttr = label.getAttribute('for');
        if (forAttr) {
            label.setAttribute('for', forAttr.replace(/\d+/, contentIndex));
        }
    });
}

function updateAllFormIndices() {
    const modulesContainer = document.getElementById('modules-container');
    modulesContainer.querySelectorAll('.module-form').forEach((module, moduleIndex) => {
        updateFormIndices(module, moduleIndex);
        
        module.querySelectorAll('.content-form').forEach((content, contentIndex) => {
            updateContentFormIndices(content, moduleIndex, contentIndex);
        });
    });
}

function showContentTypeFields(container) {
    const selectedType = container.find('.content-type-select').val();
    container.find('[class*="content-type-"]').hide();
    container.find(`.content-type-${selectedType}`).show();
}
</script>
{% endblock %}
