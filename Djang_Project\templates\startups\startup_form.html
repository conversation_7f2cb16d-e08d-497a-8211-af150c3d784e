{% extends "startups/base_startups.html" %}

{% block title %}{% if form.instance.pk %}Modifier{% else %}C<PERSON>er{% endif %} une Startup | {{ block.super }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'startups:startup_list' %}">Startups</a></li>
<li class="breadcrumb-item active">{% if form.instance.pk %}Modifier{% else %}Créer{% endif %} une Startup</li>
{% endblock %}

{% block startup_content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <h1 class="card-title">{% if form.instance.pk %}Modifier{% else %}Créer{% endif %} une Startup</h1>
                
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    <div class="mb-3">
                        <label for="{{ form.name.id_for_label }}" class="form-label">Nom de la Startup *</label>
                        {{ form.name }}
                        {% if form.name.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.name.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.tagline.id_for_label }}" class="form-label">Slogan *</label>
                        {{ form.tagline }}
                        {% if form.tagline.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.tagline.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">Description *</label>
                        {{ form.description }}
                        {% if form.description.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.description.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.industry.id_for_label }}" class="form-label">Industrie *</label>
                                {{ form.industry }}
                                {% if form.industry.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.industry.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.stage.id_for_label }}" class="form-label">Stade *</label>
                                {{ form.stage }}
                                {% if form.stage.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.stage.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.city.id_for_label }}" class="form-label">Ville *</label>
                                {{ form.city }}
                                {% if form.city.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.city.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.country.id_for_label }}" class="form-label">Pays *</label>
                                {{ form.country }}
                                {% if form.country.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.country.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.logo.id_for_label }}" class="form-label">Logo</label>
                        {{ form.logo }}
                        {% if form.logo.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.logo.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                        <div class="form-text">Format recommandé : PNG ou JPG, taille maximale 2MB</div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.website.id_for_label }}" class="form-label">Site Web</label>
                        {{ form.website }}
                        {% if form.website.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.website.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            {% if form.instance.pk %}Mettre à jour{% else %}Créer{% endif %}
                        </button>
                        <a href="{% url 'startups:startup_list' %}" class="btn btn-outline-secondary">Annuler</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
{{ block.super }}
<style>
    .form-control, .form-select {
        border-radius: 0.375rem;
    }
    textarea.form-control {
        min-height: 150px;
    }
</style>
{% endblock %}
