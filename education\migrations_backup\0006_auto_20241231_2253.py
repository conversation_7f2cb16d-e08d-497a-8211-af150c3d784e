# Generated by Django 4.2.17 on 2024-12-31 20:53

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('education', '0005_auto_20241231_2247'),
    ]

    operations = [
        migrations.CreateModel(
            name='Training',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateTimeField()),
                ('end_date', models.DateTimeField()),
                ('max_participants', models.IntegerField()),
                ('current_participants', models.IntegerField(default=0)),
                ('location', models.CharField(blank=True, max_length=255)),
                ('is_online', models.BooleanField(default=False)),
                ('meeting_link', models.URLField(blank=True)),
                ('registration_deadline', models.DateTimeField()),
                ('is_active', models.<PERSON><PERSON>anField(default=True)),
                ('description', models.TextField(help_text='Additional information about this training session')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='trainings', to='education.course')),
            ],
            options={
                'ordering': ['-start_date'],
            },
        ),
    ]
