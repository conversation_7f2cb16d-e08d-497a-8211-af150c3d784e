# Configuration de base
DEBUG=True
SECRET_KEY=django-insecure-ol6by6grt%(gv8e)8e5dqnf*w@d&163@8o(d=w%#cp%z)ba$71

# Base de données
DATABASE_URL=sqlite:///db.sqlite3
# Pour PostgreSQL en production :
# DATABASE_URL=postgresql://user:password@localhost:5432/comlab

# CORS et sécurité
ALLOWED_HOSTS=localhost,127.0.0.1
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# JWT Configuration
JWT_ACCESS_TOKEN_LIFETIME=3600  # 1 heure en secondes
JWT_REFRESH_TOKEN_LIFETIME=86400  # 1 jour en secondes

# Email (optionnel)
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# Media et Static files
MEDIA_ROOT=media
STATIC_ROOT=staticfiles

# API Configuration
API_RATE_LIMIT_ANON=100/day
API_RATE_LIMIT_USER=1000/day
