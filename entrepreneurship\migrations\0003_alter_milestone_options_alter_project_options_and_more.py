# Generated by Django 4.2.8 on 2024-12-30 07:48

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('entrepreneurship', '0002_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='milestone',
            options={'ordering': ['due_date']},
        ),
        migrations.AlterModelOptions(
            name='project',
            options={'ordering': ['start_date']},
        ),
        migrations.AlterModelOptions(
            name='resource',
            options={'ordering': ['title']},
        ),
        migrations.AlterModelOptions(
            name='startup',
            options={'ordering': ['name']},
        ),
        migrations.RemoveField(
            model_name='milestone',
            name='completed',
        ),
        migrations.RemoveField(
            model_name='milestone',
            name='completed_at',
        ),
        migrations.RemoveField(
            model_name='project',
            name='created_at',
        ),
        migrations.RemoveField(
            model_name='project',
            name='updated_at',
        ),
        migrations.RemoveField(
            model_name='resource',
            name='project',
        ),
        migrations.RemoveField(
            model_name='resource',
            name='uploaded_at',
        ),
        migrations.RemoveField(
            model_name='startup',
            name='created_at',
        ),
        migrations.RemoveField(
            model_name='startup',
            name='entrepreneur',
        ),
        migrations.RemoveField(
            model_name='startup',
            name='updated_at',
        ),
        migrations.AddField(
            model_name='milestone',
            name='assignee',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_milestones', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='milestone',
            name='completion_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='milestone',
            name='priority',
            field=models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')], default='medium', max_length=20),
        ),
        migrations.AddField(
            model_name='milestone',
            name='status',
            field=models.CharField(choices=[('not_started', 'Not Started'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('delayed', 'Delayed')], default='not_started', max_length=20),
        ),
        migrations.AddField(
            model_name='project',
            name='manager',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_projects', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='project',
            name='priority',
            field=models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')], default='medium', max_length=20),
        ),
        migrations.AddField(
            model_name='project',
            name='team_members',
            field=models.ManyToManyField(blank=True, related_name='project_memberships', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='resource',
            name='owner',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='owned_resources', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='resource',
            name='startup',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resources', to='entrepreneurship.startup'),
        ),
        migrations.AddField(
            model_name='resource',
            name='visibility',
            field=models.CharField(choices=[('public', 'Public'), ('private', 'Private'), ('team', 'Team Only')], default='private', max_length=20),
        ),
        migrations.AddField(
            model_name='startup',
            name='founder',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='founded_startups', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='startup',
            name='funding_stage',
            field=models.CharField(choices=[('pre_seed', 'Pre-Seed'), ('seed', 'Seed'), ('series_a', 'Series A'), ('series_b', 'Series B'), ('series_c', 'Series C'), ('series_d', 'Series D+'), ('ipo', 'IPO')], default='pre_seed', max_length=20),
        ),
        migrations.AddField(
            model_name='startup',
            name='pitch_deck',
            field=models.FileField(blank=True, null=True, upload_to='pitch_decks/'),
        ),
        migrations.AddField(
            model_name='startup',
            name='status',
            field=models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('pending', 'Pending')], default='pending', max_length=20),
        ),
        migrations.AddField(
            model_name='startup',
            name='team_size',
            field=models.IntegerField(default=1),
        ),
        migrations.AddField(
            model_name='startup',
            name='total_funding',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=15),
        ),
        migrations.AlterField(
            model_name='milestone',
            name='title',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='project',
            name='budget',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True),
        ),
        migrations.AlterField(
            model_name='project',
            name='end_date',
            field=models.DateField(),
        ),
        migrations.AlterField(
            model_name='project',
            name='status',
            field=models.CharField(choices=[('not_started', 'Not Started'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('on_hold', 'On Hold'), ('cancelled', 'Cancelled')], default='not_started', max_length=20),
        ),
        migrations.AlterField(
            model_name='project',
            name='title',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='resource',
            name='description',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='resource',
            name='file',
            field=models.FileField(blank=True, null=True, upload_to='startup_resources/'),
        ),
        migrations.AlterField(
            model_name='resource',
            name='resource_type',
            field=models.CharField(choices=[('document', 'Document'), ('image', 'Image'), ('video', 'Video'), ('link', 'Link'), ('other', 'Other')], max_length=20),
        ),
        migrations.AlterField(
            model_name='resource',
            name='title',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='startup',
            name='name',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='startup',
            name='website',
            field=models.URLField(blank=True, null=True),
        ),
    ]
