{% extends 'base.html' %}
{% load static %}

{% block title %}Notifications{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Notifications</h1>
        {% if notifications %}
        <form action="{% url 'notifs:mark_all_as_read' %}" method="post" class="d-inline">
            {% csrf_token %}
            <button type="submit" class="btn btn-outline-primary">
                <i class="fas fa-check-double"></i> Tout marquer comme lu
            </button>
        </form>
        {% endif %}
    </div>

    {% if notifications %}
        <div class="card">
            <div class="list-group list-group-flush">
                {% for notification in notifications %}
                    <div class="list-group-item {% if not notification.is_read %}list-group-item-light{% endif %}">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="me-3">
                                {% if notification.level == 'success' %}
                                    <i class="fas fa-check-circle text-success fs-4"></i>
                                {% elif notification.level == 'warning' %}
                                    <i class="fas fa-exclamation-triangle text-warning fs-4"></i>
                                {% elif notification.level == 'error' %}
                                    <i class="fas fa-times-circle text-danger fs-4"></i>
                                {% else %}
                                    <i class="fas fa-info-circle text-info fs-4"></i>
                                {% endif %}
                            </div>
                            <div class="flex-grow-1">
                                <div class="d-flex w-100 justify-content-between">
                                    <h5 class="mb-1">{{ notification.title }}</h5>
                                    <small class="text-muted">{{ notification.created_at|date:"d/m/Y H:i" }}</small>
                                </div>
                                <p class="mb-1">{{ notification.message }}</p>
                                {% if notification.action_url %}
                                    <a href="{{ notification.action_url }}" class="btn btn-sm btn-primary mt-2">
                                        {{ notification.action_text }}
                                    </a>
                                {% endif %}
                            </div>
                            {% if not notification.is_read %}
                                <div class="ms-3">
                                    <form action="{% url 'notifs:mark_as_read' notification.pk %}" method="post" class="d-inline">
                                        {% csrf_token %}
                                        <button type="submit" class="btn btn-sm btn-outline-secondary">
                                            <i class="fas fa-check"></i>
                                        </button>
                                    </form>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>

        {% if is_paginated %}
            <nav aria-label="Pagination" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Précédent</a>
                        </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">Suivant</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        {% endif %}
    {% else %}
        <div class="text-center py-5">
            <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">Aucune notification</h4>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gestion des formulaires de marquage comme lu
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            fetch(this.action, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': this.querySelector('[name=csrfmiddlewaretoken]').value,
                },
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    if (this.action.includes('mark-all-as-read')) {
                        location.reload();
                    } else {
                        const item = this.closest('.list-group-item');
                        item.classList.remove('list-group-item-light');
                        this.closest('.ms-3').remove();
                    }
                }
            })
            .catch(error => console.error('Error:', error));
        });
    });
});
</script>
{% endblock %}
