{"name": "typescript-api-example", "version": "1.0.0", "description": "Example of using APIs with TypeScript", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "ts-node src/index.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --config=jest.config.js --testMatch='**/*.integration.test.ts'"}, "dependencies": {"axios": "^1.6.2", "typescript": "^5.3.2"}, "devDependencies": {"@types/jest": "^29.5.10", "@types/node": "^20.10.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.1"}}