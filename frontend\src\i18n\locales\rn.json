{"common": {"loading": "Gukora...", "error": "<PERSON><PERSON><PERSON>", "success": "Byagenze neza", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "<PERSON><PERSON><PERSON><PERSON>", "save": "Kubika", "edit": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Gusiba", "search": "<PERSON><PERSON><PERSON>", "filter": "Gutoranya", "sort": "Gutondeka", "next": "<PERSON><PERSON><PERSON><PERSON>", "previous": "Ibanjirije", "close": "<PERSON><PERSON><PERSON><PERSON>", "open": "<PERSON><PERSON><PERSON><PERSON>"}, "auth": {"login": "<PERSON><PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON>", "register": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON>", "password": "Ijambo ry'ibanga", "confirmPassword": "Kwemeza ijambo ry'ibanga", "firstName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forgotPassword": "Wibagiwe ijambo ry'ibanga?", "resetPassword": "<PERSON><PERSON><PERSON><PERSON> ijambo ry'ibanga", "loginSuccess": "<PERSON><PERSON><PERSON>ra byagenze neza!", "loginError": "<PERSON><PERSON><PERSON> mu kwinjira", "registerSuccess": "Kwiyandikisha byagenze neza!", "registerError": "<PERSON>kosa mu kwiyan<PERSON><PERSON>ha", "logoutSuccess": "<PERSON><PERSON><PERSON> byagenze neza", "emailVerification": "<PERSON><PERSON><PERSON><PERSON>", "emailVerified": "<PERSON><PERSON>i ye<PERSON>!", "emailVerificationError": "Ikosa mu kugenzura"}, "navigation": {"home": "Ahabanza", "about": "Ibibazo", "programs": "<PERSON><PERSON><PERSON>", "courses": "Amasomo", "projects": "<PERSON><PERSON><PERSON><PERSON>", "equipment": "Ibikoresho", "mentorship": "Ubu<PERSON><PERSON><PERSON>", "dashboard": "Ikibaho", "profile": "Umwirondoro", "settings": "Amagenamiterere", "contact": "<PERSON><PERSON><PERSON><PERSON>"}, "dashboard": {"welcome": "<PERSON><PERSON><PERSON><PERSON> neza, {{name}}!", "overview": "Incamake", "recentActivity": "Ibikorwa bya vuba", "quickActions": "Ibikorwa by<PERSON>", "stats": "<PERSON><PERSON><PERSON><PERSON>", "notifications": "<PERSON><PERSON><PERSON><PERSON>"}, "courses": {"title": "Amasomo", "myCourses": "<PERSON><PERSON><PERSON> yan<PERSON>", "allCourses": "<PERSON><PERSON><PERSON> yose", "enroll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "enrolled": "<PERSON><PERSON><PERSON>", "progress": "<PERSON><PERSON><PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON><PERSON>", "instructor": "<PERSON><PERSON><PERSON>", "duration": "<PERSON><PERSON><PERSON>", "level": "Urwego", "category": "Icyiciro", "rating": "Amanota", "students": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lessons": "Amasomo", "quiz": "Ikibazo", "certificate": "Impamyabumenyi"}, "projects": {"title": "<PERSON><PERSON><PERSON><PERSON>", "myProjects": "<PERSON><PERSON><PERSON><PERSON> yan<PERSON>", "allProjects": "<PERSON><PERSON><PERSON><PERSON> yose", "createProject": "<PERSON><PERSON><PERSON>", "joinProject": "<PERSON><PERSON><PERSON><PERSON>", "projectDetails": "<PERSON><PERSON><PERSON> y'um<PERSON><PERSON>", "team": "Itsinda", "milestones": "Intego", "tasks": "<PERSON><PERSON><PERSON>", "funding": "Inkunga", "status": "<PERSON><PERSON> bi<PERSON>", "category": "Icyiciro", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "members": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "progress": "<PERSON><PERSON><PERSON><PERSON>"}, "equipment": {"title": "Ibikoresho", "reserve": "Kubika", "available": "Biraboneka", "reserved": "Byabitswe", "maintenance": "<PERSON><PERSON>", "myReservations": "<PERSON><PERSON><PERSON> nabit<PERSON>", "reservationDetails": "Amak<PERSON> y'uko nabitse", "startTime": "<PERSON><PERSON><PERSON> cyo <PERSON>", "endTime": "<PERSON><PERSON><PERSON> cyo kurang<PERSON>", "purpose": "Intego", "notes": "<PERSON><PERSON><PERSON><PERSON>"}, "mentorship": {"title": "Ubu<PERSON><PERSON><PERSON>", "myMentors": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myMentees": "<PERSON><PERSON>", "findMentor": "<PERSON><PERSON><PERSON>", "becomeMentor": "<PERSON><PERSON>", "requestMentor": "<PERSON><PERSON>", "sessions": "Ibiganiro", "scheduleSession": "<PERSON><PERSON>ganya <PERSON>", "expertise": "<PERSON><PERSON><PERSON><PERSON>", "experience": "<PERSON><PERSON><PERSON><PERSON>", "availability": "<PERSON><PERSON><PERSON>"}, "profile": {"title": "Umwirondoro", "editProfile": "<PERSON><PERSON><PERSON><PERSON> um<PERSON>", "personalInfo": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "professionalInfo": "Amak<PERSON> y'umwuga", "skills": "<PERSON><PERSON><PERSON><PERSON>", "interests": "<PERSON><PERSON><PERSON> ushaka", "bio": "Ubuzima", "location": "<PERSON><PERSON>z<PERSON>", "institution": "Ikigo", "phone": "Telefoni", "avatar": "<PERSON><PERSON>", "changeAvatar": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>"}, "settings": {"title": "Amagenamiterere", "general": "<PERSON><PERSON><PERSON>", "notifications": "<PERSON><PERSON><PERSON><PERSON>", "privacy": "Ibanga", "security": "Umutekano", "language": "<PERSON><PERSON><PERSON><PERSON>", "theme": "Imiterere", "account": "<PERSON><PERSON>", "preferences": "<PERSON><PERSON><PERSON>"}, "notifications": {"title": "<PERSON><PERSON><PERSON><PERSON>", "markAllRead": "<PERSON><PERSON> byose", "noNotifications": "<PERSON><PERSON>", "newCourse": "Isomo rishya r<PERSON>", "projectUpdate": "<PERSON><PERSON><PERSON>", "mentorshipRequest": "<PERSON><PERSON>", "equipmentReservation": "Ku<PERSON><PERSON> i<PERSON>", "systemUpdate": "<PERSON><PERSON><PERSON><PERSON> si<PERSON>"}, "errors": {"pageNotFound": "<PERSON><PERSON><PERSON><PERSON>", "serverError": "<PERSON><PERSON><PERSON> rya se<PERSON>i", "networkError": "Ikosa ry'umuyoboro", "unauthorized": "Ntabwo wemerewe", "forbidden": "Birabujijwe", "validationError": "Ikosa mu kugenzura", "unknownError": "<PERSON><PERSON><PERSON> r<PERSON>"}, "hero": {"title": "<PERSON><PERSON> mu Burundi", "subtitle": "Urubuga rwa mbere rw'ubufatanye mu buhanga, ubucuruzi n'amahugurwa ya tekinoroji mu Burundi", "cta": {"start": "<PERSON><PERSON>", "demo": "<PERSON><PERSON>"}}, "footer": {"description": "Community Laboratory Burundi - Guhindura ubuhanga mu Burundi", "quickLinks": "<PERSON><PERSON><PERSON>", "contact": "<PERSON><PERSON><PERSON><PERSON>", "followUs": "<PERSON><PERSON><PERSON><PERSON>", "allRightsReserved": "Ubureng<PERSON><PERSON><PERSON> b<PERSON><PERSON> b<PERSON><PERSON>swe"}}