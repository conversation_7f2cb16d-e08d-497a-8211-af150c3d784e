# Generated by Django 4.2.8 on 2024-12-30 07:48

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('education', '0002_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='course',
            options={'ordering': ['title']},
        ),
        migrations.AlterModelOptions(
            name='enrollment',
            options={'ordering': ['-enrolled_at']},
        ),
        migrations.AlterModelOptions(
            name='progress',
            options={'ordering': ['module__order']},
        ),
        migrations.RemoveField(
            model_name='course',
            name='created_at',
        ),
        migrations.RemoveField(
            model_name='course',
            name='mentor',
        ),
        migrations.RemoveField(
            model_name='course',
            name='students',
        ),
        migrations.RemoveField(
            model_name='course',
            name='updated_at',
        ),
        migrations.RemoveField(
            model_name='enrollment',
            name='completed',
        ),
        migrations.RemoveField(
            model_name='progress',
            name='completed',
        ),
        migrations.AddField(
            model_name='course',
            name='instructor',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='courses_teaching', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='course',
            name='level',
            field=models.CharField(choices=[('beginner', 'Beginner'), ('intermediate', 'Intermediate'), ('advanced', 'Advanced')], default='beginner', max_length=20),
        ),
        migrations.AddField(
            model_name='course',
            name='max_students',
            field=models.IntegerField(default=30),
        ),
        migrations.AddField(
            model_name='enrollment',
            name='is_completed',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='enrollment',
            name='status',
            field=models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('completed', 'Completed')], default='pending', max_length=20),
        ),
        migrations.AddField(
            model_name='progress',
            name='is_completed',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='progress',
            name='notes',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='progress',
            name='score',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True),
        ),
        migrations.AddField(
            model_name='progress',
            name='started_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='progress',
            name='status',
            field=models.CharField(choices=[('not_started', 'Not Started'), ('in_progress', 'In Progress'), ('completed', 'Completed')], default='not_started', max_length=20),
        ),
        migrations.AlterField(
            model_name='course',
            name='title',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='enrollment',
            name='course',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='enrollments', to='education.course'),
        ),
        migrations.AlterField(
            model_name='enrollment',
            name='student',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='course_enrollments', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='module',
            name='order',
            field=models.IntegerField(),
        ),
        migrations.AlterField(
            model_name='module',
            name='title',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='progress',
            name='enrollment',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='progress_records', to='education.enrollment'),
        ),
        migrations.AlterField(
            model_name='progress',
            name='module',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='progress_records', to='education.module'),
        ),
        migrations.AlterUniqueTogether(
            name='module',
            unique_together={('course', 'order')},
        ),
    ]
