{% extends "organizations/base_organizations.html" %}

{% block title %}{{ competition.title }} | {{ block.super }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'organizations:competition_list' %}">Compétitions</a></li>
<li class="breadcrumb-item active">{{ competition.title }}</li>
{% endblock %}

{% block organization_content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1>{{ competition.title }}</h1>
        <span class="badge {% if competition.status == 'upcoming' %}bg-info{% elif competition.status == 'ongoing' %}bg-success{% elif competition.status == 'completed' %}bg-secondary{% else %}bg-danger{% endif %} mb-3">
            {{ competition.get_status_display }}
        </span>
        <span class="badge bg-primary ms-2">{{ competition.get_type_display }}</span>
    </div>
    <div class="col-md-4 text-end">
        {% if user.is_authenticated and user.is_staff %}
        <a href="{% url 'organizations:competition_update' competition.pk %}" class="btn btn-primary">
            <i class="fas fa-edit"></i> Modifier
        </a>
        {% endif %}
    </div>
</div>

<div class="row">
    <!-- Informations principales -->
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">Description</h5>
                <p class="card-text">{{ competition.description|linebreaks }}</p>
                
                <h5 class="card-title mt-4">Prix</h5>
                <p class="card-text prize-amount">{{ competition.prize|linebreaks }}</p>
                
                <h5 class="card-title mt-4">Dates importantes</h5>
                <div class="row">
                    <div class="col-md-6">
                        <p>
                            <strong><i class="fas fa-calendar"></i> Début:</strong><br>
                            {{ competition.start_date|date:"d F Y H:i" }}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <p>
                            <strong><i class="fas fa-flag-checkered"></i> Fin:</strong><br>
                            {{ competition.end_date|date:"d F Y H:i" }}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Participants -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Participants ({{ competition.registrations.count }}/{{ competition.max_participants }})</h5>
                
                {% if competition.registrations.exists %}
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Startup</th>
                                <th>Date d'inscription</th>
                                <th>Statut</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for registration in competition.registrations.all %}
                            <tr>
                                <td>
                                    <a href="{% url 'startups:startup_detail' registration.startup.slug %}">
                                        {{ registration.startup.name }}
                                    </a>
                                </td>
                                <td>{{ registration.registered_at|date:"d M Y" }}</td>
                                <td>
                                    <span class="badge {% if registration.status == 'confirmed' %}bg-success{% elif registration.status == 'pending' %}bg-warning{% else %}bg-danger{% endif %}">
                                        {{ registration.get_status_display }}
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">Aucun participant inscrit pour le moment.</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-md-4">
        <!-- Actions -->
        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">Actions</h5>
                
                {% if user.is_authenticated %}
                    {% if user.startup %}
                        {% if not competition.registrations.filter(startup=user.startup).exists and competition.status == 'upcoming' %}
                            <a href="{% url 'organizations:competition_register' competition.pk %}" class="btn btn-success w-100 mb-3">
                                S'inscrire
                            </a>
                        {% else %}
                            <div class="alert alert-info">
                                Votre startup est déjà inscrite à cette compétition.
                            </div>
                        {% endif %}
                    {% else %}
                        <div class="alert alert-warning">
                            Vous devez être associé à une startup pour participer.
                        </div>
                    {% endif %}
                {% else %}
                    <div class="alert alert-warning">
                        Connectez-vous pour participer à cette compétition.
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Organisateur -->
        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">Organisateur</h5>
                <p class="mb-0">
                    <i class="fas fa-building"></i>
                    {{ competition.organization.get_full_name }}
                </p>
                <p class="mb-0">
                    <i class="fas fa-envelope"></i>
                    <a href="mailto:{{ competition.organization.email }}">
                        {{ competition.organization.email }}
                    </a>
                </p>
            </div>
        </div>

        <!-- Statistiques -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Statistiques</h5>
                <div class="d-flex justify-content-between mb-2">
                    <span>Places restantes:</span>
                    <span class="fw-bold">
                        {{ competition.max_participants|sub:competition.registrations.count }}
                    </span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>Taux de remplissage:</span>
                    <span class="fw-bold">
                        {{ competition.registrations.count|div:competition.max_participants|multiply:100|floatformat:0 }}%
                    </span>
                </div>
                <div class="progress">
                    <div class="progress-bar" role="progressbar" 
                         style="width: {{ competition.registrations.count|div:competition.max_participants|multiply:100 }}%"
                         aria-valuenow="{{ competition.registrations.count|div:competition.max_participants|multiply:100 }}"
                         aria-valuemin="0" aria-valuemax="100">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
