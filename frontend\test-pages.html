<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test des Pages - Community Laboratory Burundi</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
        }
        .card h3 {
            margin: 0 0 12px 0;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .card p {
            color: #64748b;
            margin: 0 0 16px 0;
            line-height: 1.5;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: background 0.2s;
        }
        .btn:hover {
            background: #2563eb;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 8px;
        }
        .status.new {
            background: #dcfce7;
            color: #166534;
        }
        .status.existing {
            background: #dbeafe;
            color: #1e40af;
        }
        .features {
            list-style: none;
            padding: 0;
            margin: 12px 0;
        }
        .features li {
            padding: 4px 0;
            color: #64748b;
            font-size: 14px;
        }
        .features li:before {
            content: "✓";
            color: #10b981;
            font-weight: bold;
            margin-right: 8px;
        }
        .footer {
            text-align: center;
            padding: 40px;
            background: white;
            border-radius: 12px;
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🇧🇮 Community Laboratory Burundi</h1>
        <p>Test de toutes les pages créées - Frontend complet</p>
        <p><strong>Serveur de développement :</strong> <a href="http://localhost:5173" style="color: #fbbf24;">http://localhost:5173</a></p>
    </div>

    <div class="grid">
        <div class="card">
            <h3>🏠 Accueil <span class="status new">NOUVEAU</span></h3>
            <p>Page d'accueil avec vue d'ensemble de tous les modules et navigation vers toutes les fonctionnalités.</p>
            <ul class="features">
                <li>Vue d'ensemble des modules</li>
                <li>Statistiques en temps réel</li>
                <li>Navigation rapide</li>
                <li>Design responsive</li>
            </ul>
            <a href="http://localhost:5173/" class="btn">Tester la page</a>
        </div>

        <div class="card">
            <h3>📚 Cours & Formations <span class="status new">NOUVEAU</span></h3>
            <p>Module complet d'éducation avec cours, instructeurs, progression et certifications.</p>
            <ul class="features">
                <li>Liste des cours avec filtres</li>
                <li>Détails de cours complets</li>
                <li>Système d'inscription</li>
                <li>Suivi de progression</li>
            </ul>
            <a href="http://localhost:5173/courses" class="btn">Tester la page</a>
        </div>

        <div class="card">
            <h3>🚀 Projets Entrepreneuriaux <span class="status new">NOUVEAU</span></h3>
            <p>Plateforme pour découvrir, soutenir et collaborer sur des projets innovants burundais.</p>
            <ul class="features">
                <li>Galerie de projets</li>
                <li>Filtres par secteur/stade</li>
                <li>Système de financement</li>
                <li>Collaboration équipe</li>
            </ul>
            <a href="http://localhost:5173/projects" class="btn">Tester la page</a>
        </div>

        <div class="card">
            <h3>🔧 Fab Lab & Équipements <span class="status new">NOUVEAU</span></h3>
            <p>Gestion complète des équipements de fabrication numérique avec réservations.</p>
            <ul class="features">
                <li>Catalogue d'équipements</li>
                <li>Système de réservation</li>
                <li>Gestion maintenance</li>
                <li>Certifications requises</li>
            </ul>
            <a href="http://localhost:5173/lab/equipment" class="btn">Tester la page</a>
        </div>

        <div class="card">
            <h3>💬 Forum Communautaire <span class="status new">NOUVEAU</span></h3>
            <p>Espace d'échange et de collaboration pour la communauté burundaise d'innovation.</p>
            <ul class="features">
                <li>Catégories thématiques</li>
                <li>Discussions interactives</li>
                <li>Système de votes</li>
                <li>Membres actifs</li>
            </ul>
            <a href="http://localhost:5173/forum" class="btn">Tester la page</a>
        </div>

        <div class="card">
            <h3>🔔 Centre de Notifications <span class="status new">NOUVEAU</span></h3>
            <p>Système unifié de notifications avec filtres intelligents et paramètres granulaires.</p>
            <ul class="features">
                <li>Notifications temps réel</li>
                <li>Filtres avancés</li>
                <li>Actions rapides</li>
                <li>Paramètres personnalisés</li>
            </ul>
            <a href="http://localhost:5173/notifications" class="btn">Tester la page</a>
        </div>

        <div class="card">
            <h3>🏢 Organisations Partenaires <span class="status new">NOUVEAU</span></h3>
            <p>Réseau de partenaires locaux et internationaux soutenant l'innovation au Burundi.</p>
            <ul class="features">
                <li>Profils détaillés</li>
                <li>Types de partenariats</li>
                <li>Projets collaboratifs</li>
                <li>Métriques d'impact</li>
            </ul>
            <a href="http://localhost:5173/organizations" class="btn">Tester la page</a>
        </div>

        <div class="card">
            <h3>🔍 Recherche Globale <span class="status new">NOUVEAU</span></h3>
            <p>Moteur de recherche intelligent pour trouver tout contenu dans l'écosystème.</p>
            <ul class="features">
                <li>Recherche multi-catégories</li>
                <li>Filtres avancés</li>
                <li>Suggestions intelligentes</li>
                <li>Historique personnel</li>
            </ul>
            <a href="http://localhost:5173/search" class="btn">Tester la page</a>
        </div>

        <div class="card">
            <h3>📊 Dashboard Analytics <span class="status existing">EXISTANT</span></h3>
            <p>Tableaux de bord avec métriques et analytics pour tous les modules.</p>
            <ul class="features">
                <li>4 dashboards spécialisés</li>
                <li>Métriques temps réel</li>
                <li>Visualisations avancées</li>
                <li>Rapports personnalisés</li>
            </ul>
            <a href="http://localhost:5173/dashboard" class="btn">Tester la page</a>
        </div>

        <!-- NOUVELLES PAGES DÉTAILLÉES -->
        <div class="card">
            <h3>🎓 Apprentissage Cours <span class="status new">NOUVEAU</span></h3>
            <p>Interface d'apprentissage complète avec vidéos, quiz et suivi de progression.</p>
            <ul class="features">
                <li>Lecteur vidéo intégré</li>
                <li>Transcriptions et ressources</li>
                <li>Quiz interactifs</li>
                <li>Plan de cours détaillé</li>
            </ul>
            <a href="http://localhost:5173/courses/2/learn" class="btn">Tester la page</a>
        </div>

        <div class="card">
            <h3>🚀 Détail Projet <span class="status new">NOUVEAU</span></h3>
            <p>Page complète de projet avec équipe, financement et jalons détaillés.</p>
            <ul class="features">
                <li>Profil équipe complet</li>
                <li>Suivi financement</li>
                <li>Timeline des jalons</li>
                <li>Actualités et FAQ</li>
            </ul>
            <a href="http://localhost:5173/projects/1" class="btn">Tester la page</a>
        </div>

        <div class="card">
            <h3>🔧 Détail Équipement <span class="status new">NOUVEAU</span></h3>
            <p>Fiche technique complète avec spécifications, planning et avis utilisateurs.</p>
            <ul class="features">
                <li>Spécifications détaillées</li>
                <li>Planning de réservation</li>
                <li>Historique maintenance</li>
                <li>Avis et projets réalisés</li>
            </ul>
            <a href="http://localhost:5173/lab/equipment/1" class="btn">Tester la page</a>
        </div>

        <div class="card">
            <h3>📅 Réservation Équipement <span class="status new">NOUVEAU</span></h3>
            <p>Système de réservation complet avec calcul de coûts et gestion matériaux.</p>
            <ul class="features">
                <li>Sélection date/heure</li>
                <li>Calcul coûts automatique</li>
                <li>Gestion matériaux</li>
                <li>Conditions d'utilisation</li>
            </ul>
            <a href="http://localhost:5173/lab/equipment/1/reserve" class="btn">Tester la page</a>
        </div>

        <div class="card">
            <h3>💬 Catégorie Forum <span class="status new">NOUVEAU</span></h3>
            <p>Vue catégorie avec discussions filtrées, recherche et membres actifs.</p>
            <ul class="features">
                <li>Liste discussions filtrée</li>
                <li>Recherche avancée</li>
                <li>Statistiques catégorie</li>
                <li>Membres actifs</li>
            </ul>
            <a href="http://localhost:5173/forum/category/1" class="btn">Tester la page</a>
        </div>

        <div class="card">
            <h3>✍️ Créer Discussion <span class="status new">NOUVEAU</span></h3>
            <p>Éditeur complet pour créer des discussions avec tags et options avancées.</p>
            <ul class="features">
                <li>Éditeur riche</li>
                <li>Système de tags</li>
                <li>Prévisualisation</li>
                <li>Brouillons</li>
            </ul>
            <a href="http://localhost:5173/forum/create" class="btn">Tester la page</a>
        </div>

        <div class="card">
            <h3>⚙️ Paramètres Notifications <span class="status new">NOUVEAU</span></h3>
            <p>Configuration granulaire des notifications par type et canal.</p>
            <ul class="features">
                <li>Canaux multiples</li>
                <li>Types granulaires</li>
                <li>Heures silencieuses</li>
                <li>Test notifications</li>
            </ul>
            <a href="http://localhost:5173/notifications/settings" class="btn">Tester la page</a>
        </div>
    </div>

    <!-- PAGE DE TEST API -->
    <div class="card" style="border: 3px solid #10b981; background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);">
        <h3>🔧 Test Connexion API <span class="status new">NOUVEAU</span></h3>
        <p>Page de test pour vérifier la connectivité avec le backend Django.</p>
        <ul class="features">
            <li>Test de tous les endpoints</li>
            <li>Mesure des temps de réponse</li>
            <li>Diagnostic des erreurs</li>
            <li>Instructions de configuration</li>
        </ul>
        <a href="http://localhost:5173/api-test" class="btn" style="background: #10b981;">🚀 TESTER LES APIS</a>
    </div>

    <div class="footer">
        <h2>🎉 TOUTES LES PAGES SONT MAINTENANT COMPLÈTES !</h2>
        <p><strong>17 pages détaillées</strong> avec navigation complète et fonctionnalités avancées</p>
        <p>
            <strong>Status :</strong>
            <span style="color: #10b981;">✅ Build réussi</span> •
            <span style="color: #10b981;">✅ TypeScript 0 erreur</span> •
            <span style="color: #10b981;">✅ Routes configurées</span> •
            <span style="color: #10b981;">✅ Pages détaillées</span>
        </p>
        <p style="margin-top: 20px;">
            <strong>Pages principales :</strong> 9 modules de base<br>
            <strong>Pages détaillées :</strong> 7 pages d'actions spécifiques<br>
            <strong>Navigation :</strong> 100% fonctionnelle entre toutes les pages<br>
            <strong>Responsive :</strong> Adaptatif sur tous les écrans
        </p>
        <p style="margin-top: 20px;">
            <strong>Prochaines étapes :</strong><br>
            1. Démarrer le serveur : <code>npm run dev</code><br>
            2. Tester chaque page individuellement<br>
            3. Vérifier la navigation entre les pages<br>
            4. Connecter au backend Django<br>
            5. Personnaliser avec vos données réelles
        </p>
    </div>
</body>
</html>
