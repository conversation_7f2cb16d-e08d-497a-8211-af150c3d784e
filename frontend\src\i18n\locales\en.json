{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "edit": "Edit", "delete": "Delete", "search": "Search", "filter": "Filter", "sort": "Sort", "next": "Next", "previous": "Previous", "close": "Close", "open": "Open"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "register": "Register", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "firstName": "First Name", "lastName": "Last Name", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "loginSuccess": "Login successful!", "loginError": "Login error", "registerSuccess": "Registration successful!", "registerError": "Registration error", "logoutSuccess": "Logout successful", "emailVerification": "Email Verification", "emailVerified": "Email verified!", "emailVerificationError": "Verification error"}, "navigation": {"home": "Home", "about": "About", "programs": "Programs", "courses": "Courses", "projects": "Projects", "equipment": "Equipment", "mentorship": "Mentorship", "dashboard": "Dashboard", "profile": "Profile", "settings": "Settings", "contact": "Contact"}, "dashboard": {"welcome": "Welcome, {{name}}!", "overview": "Overview", "recentActivity": "Recent Activity", "quickActions": "Quick Actions", "stats": "Statistics", "notifications": "Notifications"}, "courses": {"title": "Courses", "myCourses": "My Courses", "allCourses": "All Courses", "enroll": "Enroll", "enrolled": "Enrolled", "progress": "Progress", "completed": "Completed", "instructor": "<PERSON><PERSON><PERSON><PERSON>", "duration": "Duration", "level": "Level", "category": "Category", "rating": "Rating", "students": "Students", "lessons": "Lessons", "quiz": "Quiz", "certificate": "Certificate"}, "projects": {"title": "Projects", "myProjects": "My Projects", "allProjects": "All Projects", "createProject": "Create Project", "joinProject": "Join", "projectDetails": "Project Details", "team": "Team", "milestones": "Milestones", "tasks": "Tasks", "funding": "Funding", "status": "Status", "category": "Category", "owner": "Owner", "members": "Members", "progress": "Progress"}, "equipment": {"title": "Equipment", "reserve": "Reserve", "available": "Available", "reserved": "Reserved", "maintenance": "Maintenance", "myReservations": "My Reservations", "reservationDetails": "Reservation Details", "startTime": "Start Time", "endTime": "End Time", "purpose": "Purpose", "notes": "Notes"}, "mentorship": {"title": "Mentorship", "myMentors": "My Mentors", "myMentees": "My Mentees", "findMentor": "Find Mentor", "becomeMentor": "Become Mentor", "requestMentor": "Request Mentor", "sessions": "Sessions", "scheduleSession": "Schedule Session", "expertise": "Expertise", "experience": "Experience", "availability": "Availability"}, "profile": {"title": "Profile", "editProfile": "Edit Profile", "personalInfo": "Personal Information", "professionalInfo": "Professional Information", "skills": "Skills", "interests": "Interests", "bio": "Biography", "location": "Location", "institution": "Institution", "phone": "Phone", "avatar": "Profile Picture", "changeAvatar": "Change Picture"}, "settings": {"title": "Settings", "general": "General", "notifications": "Notifications", "privacy": "Privacy", "security": "Security", "language": "Language", "theme": "Theme", "account": "Account", "preferences": "Preferences"}, "notifications": {"title": "Notifications", "markAllRead": "<PERSON> as <PERSON>", "noNotifications": "No notifications", "newCourse": "New course available", "projectUpdate": "Project update", "mentorshipRequest": "Mentorship request", "equipmentReservation": "Equipment reservation", "systemUpdate": "System update"}, "errors": {"pageNotFound": "Page Not Found", "serverError": "Server Error", "networkError": "Network Error", "unauthorized": "Unauthorized", "forbidden": "Forbidden", "validationError": "Validation Error", "unknownError": "Unknown Error"}, "hero": {"title": "Revolutionize Innovation in Burundi", "subtitle": "The first collaborative platform for innovation, entrepreneurship and technological training in Burundi", "cta": {"start": "Get Started Free", "demo": "Watch Demo"}}, "footer": {"description": "Community Laboratory Burundi - Revolutionizing innovation in Burundi", "quickLinks": "Quick Links", "contact": "Contact", "followUs": "Follow Us", "allRightsReserved": "All rights reserved"}}