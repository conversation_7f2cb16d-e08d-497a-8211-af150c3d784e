{% extends 'base.html' %}

{% block title %}{{ organization.name }} - Community Lab{% endblock %}

{% block content %}
<div class="position-relative mb-4">
    {% if organization.cover_image %}
        <img src="{{ organization.cover_image.url }}" class="w-100" style="height: 300px; object-fit: cover;" 
             alt="{{ organization.name }} couverture">
    {% else %}
        <div class="bg-light w-100" style="height: 300px;"></div>
    {% endif %}
    
    {% if organization.logo %}
        <img src="{{ organization.logo.url }}" 
             class="position-absolute rounded-circle border border-4 border-white" 
             style="width: 150px; height: 150px; bottom: -75px; left: 50px;" 
             alt="{{ organization.name }} logo">
    {% endif %}
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="mb-0">{{ organization.name }}</h1>
                        <p class="text-muted mb-0">
                            {{ organization.get_type_display }} • {{ organization.get_sector_display }}
                        </p>
                    </div>
                    {% if user.is_authenticated and user.is_staff %}
                        <a href="{% url 'organizations:organization_edit' organization.id %}" 
                           class="btn btn-outline-primary">
                            <i class="fas fa-edit"></i> Modifier
                        </a>
                    {% endif %}
                </div>

                <div class="mb-4">
                    <h5>À propos</h5>
                    {{ organization.description|linebreaks }}
                </div>

                <div class="mb-4">
                    <h5>Informations</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-map-marker-alt"></i>
                            <strong>Localisation :</strong>
                            {{ organization.location }}
                        </li>
                        {% if organization.website %}
                            <li class="mb-2">
                                <i class="fas fa-globe"></i>
                                <strong>Site web :</strong>
                                <a href="{{ organization.website }}" target="_blank" rel="noopener noreferrer">
                                    {{ organization.website }}
                                </a>
                            </li>
                        {% endif %}
                        {% if organization.email %}
                            <li class="mb-2">
                                <i class="fas fa-envelope"></i>
                                <strong>Email :</strong>
                                <a href="mailto:{{ organization.email }}">{{ organization.email }}</a>
                            </li>
                        {% endif %}
                        {% if organization.phone %}
                            <li class="mb-2">
                                <i class="fas fa-phone"></i>
                                <strong>Téléphone :</strong>
                                <a href="tel:{{ organization.phone }}">{{ organization.phone }}</a>
                            </li>
                        {% endif %}
                        <li>
                            <i class="fas fa-calendar-alt"></i>
                            <strong>Membre depuis :</strong>
                            {{ organization.created_at|date:"F Y" }}
                        </li>
                    </ul>
                </div>

                {% if organization.social_links %}
                    <div class="mb-4">
                        <h5>Réseaux sociaux</h5>
                        <div class="d-flex gap-2">
                            {% if organization.social_links.facebook %}
                                <a href="{{ organization.social_links.facebook }}" class="btn btn-outline-primary" 
                                   target="_blank" rel="noopener noreferrer">
                                    <i class="fab fa-facebook"></i>
                                </a>
                            {% endif %}
                            {% if organization.social_links.twitter %}
                                <a href="{{ organization.social_links.twitter }}" class="btn btn-outline-info" 
                                   target="_blank" rel="noopener noreferrer">
                                    <i class="fab fa-twitter"></i>
                                </a>
                            {% endif %}
                            {% if organization.social_links.linkedin %}
                                <a href="{{ organization.social_links.linkedin }}" class="btn btn-outline-primary" 
                                   target="_blank" rel="noopener noreferrer">
                                    <i class="fab fa-linkedin"></i>
                                </a>
                            {% endif %}
                            {% if organization.social_links.instagram %}
                                <a href="{{ organization.social_links.instagram }}" class="btn btn-outline-danger" 
                                   target="_blank" rel="noopener noreferrer">
                                    <i class="fab fa-instagram"></i>
                                </a>
                            {% endif %}
                        </div>
                    </div>
                {% endif %}

                {% if organization.members.exists %}
                    <div class="mb-4">
                        <h5>Membres</h5>
                        <div class="row">
                            {% for member in organization.members.all %}
                                <div class="col-md-6 mb-3">
                                    <div class="d-flex align-items-center">
                                        {% if member.profile.avatar %}
                                            <img src="{{ member.profile.avatar.url }}" 
                                                 class="rounded-circle me-3" 
                                                 style="width: 50px; height: 50px;" 
                                                 alt="{{ member.get_full_name }}">
                                        {% endif %}
                                        <div>
                                            <h6 class="mb-0">
                                                <a href="{% url 'accounts:profile_detail' member.username %}" 
                                                   class="text-decoration-none">
                                                    {{ member.get_full_name }}
                                                </a>
                                            </h6>
                                            <p class="text-muted mb-0">{{ member.profile.title }}</p>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>

        {% if organization.project_calls.exists %}
            <div class="card mb-4">
                <div class="card-body">
                    <h5>Appels à projets</h5>
                    <div class="list-group list-group-flush">
                        {% for call in organization.project_calls.all %}
                            <a href="{% url 'organizations:project_call_detail' call.id %}" 
                               class="list-group-item list-group-item-action">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">{{ call.title }}</h6>
                                        <p class="mb-1 text-muted small">{{ call.description|truncatewords:20 }}</p>
                                        <small>
                                            <i class="fas fa-calendar-alt"></i>
                                            Date limite : {{ call.deadline|date:"d/m/Y" }}
                                        </small>
                                    </div>
                                    <span class="badge {% if call.status == 'OPEN' %}bg-success{% elif call.status == 'CLOSED' %}bg-danger{% else %}bg-secondary{% endif %}">
                                        {{ call.get_status_display }}
                                    </span>
                                </div>
                            </a>
                        {% endfor %}
                    </div>
                </div>
            </div>
        {% endif %}

        {% if organization.competitions.exists %}
            <div class="card mb-4">
                <div class="card-body">
                    <h5>Compétitions</h5>
                    <div class="list-group list-group-flush">
                        {% for competition in organization.competitions.all %}
                            <a href="{% url 'organizations:competition_detail' competition.id %}" 
                               class="list-group-item list-group-item-action">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">{{ competition.title }}</h6>
                                        <p class="mb-1 text-muted small">{{ competition.description|truncatewords:20 }}</p>
                                        <small>
                                            <i class="fas fa-calendar-alt"></i>
                                            Du {{ competition.start_date|date:"d/m/Y" }} au {{ competition.end_date|date:"d/m/Y" }}
                                        </small>
                                    </div>
                                    <span class="badge {% if competition.status == 'UPCOMING' %}bg-info{% elif competition.status == 'ONGOING' %}bg-success{% else %}bg-secondary{% endif %}">
                                        {{ competition.get_status_display }}
                                    </span>
                                </div>
                            </a>
                        {% endfor %}
                    </div>
                </div>
            </div>
        {% endif %}
    </div>

    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">Statistiques</h5>
                <div class="row text-center">
                    <div class="col-4">
                        <h3>{{ organization.project_calls.count }}</h3>
                        <p class="text-muted mb-0">Appels à projets</p>
                    </div>
                    <div class="col-4">
                        <h3>{{ organization.competitions.count }}</h3>
                        <p class="text-muted mb-0">Compétitions</p>
                    </div>
                    <div class="col-4">
                        <h3>{{ organization.members.count }}</h3>
                        <p class="text-muted mb-0">Membres</p>
                    </div>
                </div>
            </div>
        </div>

        {% if user.is_authenticated and not user.is_organization_member %}
            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title">Rejoindre l'organisation</h5>
                    <p class="card-text">
                        Vous souhaitez collaborer avec {{ organization.name }} ?
                    </p>
                    <a href="{% url 'organizations:join_request_create' organization.id %}" 
                       class="btn btn-primary w-100">
                        Envoyer une demande
                    </a>
                </div>
            </div>
        {% endif %}

        {% if similar_organizations %}
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Organisations similaires</h5>
                    <div class="list-group list-group-flush">
                        {% for similar in similar_organizations %}
                            <a href="{% url 'organizations:organization_detail' similar.id %}" 
                               class="list-group-item list-group-item-action">
                                <div class="d-flex align-items-center">
                                    {% if similar.logo %}
                                        <img src="{{ similar.logo.url }}" class="rounded-circle me-3" 
                                             style="width: 40px; height: 40px;" alt="{{ similar.name }}">
                                    {% endif %}
                                    <div>
                                        <h6 class="mb-0">{{ similar.name }}</h6>
                                        <small class="text-muted">{{ similar.get_sector_display }}</small>
                                    </div>
                                </div>
                            </a>
                        {% endfor %}
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
{% endblock %}
