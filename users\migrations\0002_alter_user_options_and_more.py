# Generated by Django 4.2.8 on 2024-12-30 07:48

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('organizations', '0003_alter_callforproject_options_and_more'),
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='user',
            options={'ordering': ['-date_joined']},
        ),
        migrations.RemoveField(
            model_name='entrepreneur',
            name='business_stage',
        ),
        migrations.RemoveField(
            model_name='mentor',
            name='years_of_experience',
        ),
        migrations.RemoveField(
            model_name='stakeholder',
            name='investment_interests',
        ),
        migrations.RemoveField(
            model_name='user',
            name='profile_picture',
        ),
        migrations.AddField(
            model_name='entrepreneur',
            name='company_stage',
            field=models.CharField(choices=[('idea', 'Idea Stage'), ('mvp', 'MVP'), ('early', 'Early Stage'), ('growth', 'Growth Stage'), ('mature', 'Mature')], default='idea', max_length=50),
        ),
        migrations.AddField(
            model_name='entrepreneur',
            name='company_website',
            field=models.URLField(blank=True),
        ),
        migrations.AddField(
            model_name='entrepreneur',
            name='interests',
            field=models.TextField(blank=True, help_text='Areas of interest or needed support'),
        ),
        migrations.AddField(
            model_name='entrepreneur',
            name='linkedin_profile',
            field=models.URLField(blank=True),
        ),
        migrations.AddField(
            model_name='mentor',
            name='availability',
            field=models.TextField(blank=True, help_text='Describe your availability for mentoring'),
        ),
        migrations.AddField(
            model_name='mentor',
            name='experience_years',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='mentor',
            name='rating',
            field=models.FloatField(default=0.0),
        ),
        migrations.AddField(
            model_name='mentor',
            name='total_reviews',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='stakeholder',
            name='department',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='stakeholder',
            name='interests',
            field=models.TextField(blank=True, help_text='Areas of interest or expertise'),
        ),
        migrations.AddField(
            model_name='stakeholder',
            name='linkedin_profile',
            field=models.URLField(blank=True),
        ),
        migrations.AddField(
            model_name='user',
            name='address',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='user',
            name='avatar',
            field=models.ImageField(blank=True, null=True, upload_to='avatars/'),
        ),
        migrations.AddField(
            model_name='user',
            name='phone_number',
            field=models.CharField(blank=True, max_length=20),
        ),
        migrations.AlterField(
            model_name='entrepreneur',
            name='industry',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='entrepreneur',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='entrepreneur_profile', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='mentor',
            name='expertise',
            field=models.CharField(choices=[('business', 'Business Development'), ('technology', 'Technology'), ('marketing', 'Marketing'), ('finance', 'Finance'), ('legal', 'Legal'), ('other', 'Other')], default='other', max_length=20),
        ),
        migrations.AlterField(
            model_name='mentor',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='mentor_profile', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='stakeholder',
            name='organization',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='organization_stakeholders', to='organizations.organization'),
        ),
        migrations.AlterField(
            model_name='stakeholder',
            name='position',
            field=models.CharField(choices=[('manager', 'Manager'), ('director', 'Director'), ('executive', 'Executive'), ('other', 'Other')], default='other', max_length=20),
        ),
        migrations.AlterField(
            model_name='stakeholder',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='stakeholder_profile', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='user',
            name='role',
            field=models.CharField(choices=[('admin', 'Administrator'), ('mentor', 'Mentor'), ('entrepreneur', 'Entrepreneur'), ('stakeholder', 'Stakeholder'), ('student', 'Student')], default='student', max_length=20),
        ),
    ]
