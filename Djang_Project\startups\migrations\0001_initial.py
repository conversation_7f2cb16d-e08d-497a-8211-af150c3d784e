# Generated by Django 5.0 on 2024-12-16 13:40

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Industry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Nom')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('icon', models.CharField(blank=True, help_text='Classe Font Awesome (ex: fa-industry)', max_length=50)),
            ],
            options={
                'verbose_name': "Secteur d'activité",
                'verbose_name_plural': "Secteurs d'activité",
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Stage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Nom')),
                ('description', models.TextField(verbose_name='Description')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='Ordre')),
            ],
            options={
                'verbose_name': 'Stade de développement',
                'verbose_name_plural': 'Stades de développement',
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='Project',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('start_date', models.DateField()),
                ('end_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('planning', 'En planification'), ('in_progress', 'En cours'), ('completed', 'Terminé'), ('on_hold', 'En pause')], max_length=20)),
                ('budget', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('project_lead', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='led_projects', to=settings.AUTH_USER_MODEL)),
                ('team_members', models.ManyToManyField(related_name='project_memberships', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='ProjectUpdate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='updates', to='startups.project')),
            ],
        ),
        migrations.CreateModel(
            name='Startup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='Nom')),
                ('slug', models.SlugField(blank=True, max_length=255, unique=True)),
                ('logo', models.ImageField(blank=True, upload_to='startups/logos/', verbose_name='Logo')),
                ('tagline', models.CharField(default='', max_length=200, verbose_name='Slogan')),
                ('description', models.TextField(verbose_name='Description')),
                ('pitch_video', models.URLField(blank=True, verbose_name='Vidéo pitch')),
                ('address', models.CharField(default='', max_length=255, verbose_name='Adresse')),
                ('business_model', models.TextField(default='', verbose_name="Modèle d'affaires")),
                ('founding_date', models.DateField(default='2024-01-01', verbose_name='Date de création')),
                ('market_opportunity', models.TextField(default='Une opportunité de marché', verbose_name='Opportunité de marché')),
                ('competitive_advantage', models.TextField(default='', verbose_name='Avantage concurrentiel')),
                ('revenue_model', models.TextField(default='Modèle de revenus par défaut', verbose_name='Modèle de revenus')),
                ('customer_segment', models.TextField(default='', verbose_name='Segment client')),
                ('revenue', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name="Chiffre d'affaires")),
                ('funding_stage', models.CharField(choices=[('pre_seed', 'Pré-amorçage'), ('seed', 'Amorçage'), ('series-a', 'Série A'), ('series-b', 'Série B'), ('series-c', 'Série C+')], default='pre_seed', max_length=20, verbose_name='Stade de financement')),
                ('funding_raised', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='Fonds levés')),
                ('website', models.URLField(blank=True, verbose_name='Site web')),
                ('email', models.EmailField(default='<EMAIL>', max_length=254, verbose_name='Email de contact')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='Téléphone')),
                ('social_facebook', models.URLField(blank=True, verbose_name='Facebook')),
                ('social_twitter', models.URLField(blank=True, verbose_name='Twitter')),
                ('social_linkedin', models.URLField(blank=True, verbose_name='LinkedIn')),
                ('city', models.CharField(default='Bujumbura', max_length=100, verbose_name='Ville')),
                ('country', models.CharField(default='Burundi', max_length=100, verbose_name='Pays')),
                ('status', models.CharField(choices=[('draft', 'Brouillon'), ('pending', 'En attente de validation'), ('active', 'Actif'), ('inactive', 'Inactif')], default='draft', max_length=20, verbose_name='Statut')),
                ('featured', models.BooleanField(default=False, verbose_name='Mis en avant')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('founder', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='founded_startups', to=settings.AUTH_USER_MODEL, verbose_name='Fondateur')),
                ('industry', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='startups.industry', verbose_name='Secteur')),
                ('stage', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='startups.stage', verbose_name='Stade')),
            ],
            options={
                'verbose_name': 'Startup',
                'verbose_name_plural': 'Startups',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='project',
            name='startup',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='projects', to='startups.startup'),
        ),
        migrations.CreateModel(
            name='Milestone',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('target_date', models.DateField()),
                ('achieved_date', models.DateField(blank=True, null=True)),
                ('is_achieved', models.BooleanField(default=False)),
                ('proof', models.FileField(blank=True, upload_to='startups/milestone_proofs/')),
                ('notes', models.TextField(blank=True)),
                ('startup', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='milestones', to='startups.startup')),
            ],
            options={
                'verbose_name': 'Jalon',
                'verbose_name_plural': 'Jalons',
                'ordering': ['target_date'],
            },
        ),
        migrations.CreateModel(
            name='Investment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('investment_type', models.CharField(choices=[('equity', 'Actions'), ('convertible_note', 'Note convertible'), ('safe', 'SAFE'), ('debt', 'Dette'), ('grant', 'Subvention')], max_length=20)),
                ('investment_date', models.DateField()),
                ('equity_percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('valuation', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('terms', models.TextField(blank=True)),
                ('documents', models.FileField(blank=True, upload_to='startups/investment_docs/')),
                ('investor', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='investments_made', to=settings.AUTH_USER_MODEL)),
                ('startup', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='investments', to='startups.startup')),
            ],
            options={
                'verbose_name': 'Investissement',
                'verbose_name_plural': 'Investissements',
                'ordering': ['-investment_date'],
            },
        ),
        migrations.CreateModel(
            name='StartupMentor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('expertise', models.TextField(verbose_name="Domaines d'expertise")),
                ('start_date', models.DateField(auto_now_add=True)),
                ('end_date', models.DateField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('hours_per_month', models.PositiveIntegerField(default=0)),
                ('notes', models.TextField(blank=True)),
                ('mentor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('startup', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='startups.startup')),
            ],
            options={
                'verbose_name': 'Mentor de startup',
                'verbose_name_plural': 'Mentors de startup',
                'unique_together': {('startup', 'mentor')},
            },
        ),
        migrations.AddField(
            model_name='startup',
            name='mentors',
            field=models.ManyToManyField(related_name='mentored_startups', through='startups.StartupMentor', to=settings.AUTH_USER_MODEL, verbose_name='Mentors'),
        ),
        migrations.CreateModel(
            name='TeamMember',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(choices=[('founder', 'Fondateur'), ('cofounder', 'Co-fondateur'), ('cto', 'Directeur technique'), ('ceo', 'Directeur général'), ('coo', 'Directeur des opérations'), ('cfo', 'Directeur financier'), ('developer', 'Développeur'), ('designer', 'Designer'), ('marketing', 'Marketing'), ('sales', 'Commercial'), ('other', 'Autre')], max_length=20)),
                ('joined_date', models.DateField(auto_now_add=True)),
                ('equity_percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name="Pourcentage d'actions")),
                ('responsibilities', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('startup', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='startups.startup')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': "Membre de l'équipe",
                'verbose_name_plural': "Membres de l'équipe",
                'unique_together': {('startup', 'user')},
            },
        ),
        migrations.AddField(
            model_name='startup',
            name='team_members',
            field=models.ManyToManyField(related_name='startup_teams', through='startups.TeamMember', to=settings.AUTH_USER_MODEL, verbose_name="Membres de l'équipe"),
        ),
        migrations.CreateModel(
            name='MentorRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message', models.TextField(verbose_name='Message de motivation')),
                ('expertise_areas', models.TextField(verbose_name="Domaines d'expertise")),
                ('availability', models.TextField(verbose_name='Disponibilité')),
                ('status', models.CharField(choices=[('pending', 'En attente'), ('accepted', 'Acceptée'), ('rejected', 'Rejetée')], default='pending', max_length=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('reviewed_at', models.DateTimeField(blank=True, null=True)),
                ('mentor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='mentor_requests', to=settings.AUTH_USER_MODEL)),
                ('reviewed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_mentor_requests', to=settings.AUTH_USER_MODEL)),
                ('startup', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='mentor_requests', to='startups.startup')),
            ],
            options={
                'ordering': ['-created_at'],
                'unique_together': {('startup', 'mentor', 'status')},
            },
        ),
        migrations.CreateModel(
            name='JoinRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(choices=[('founder', 'Fondateur'), ('cofounder', 'Co-fondateur'), ('cto', 'Directeur technique'), ('ceo', 'Directeur général'), ('coo', 'Directeur des opérations'), ('cfo', 'Directeur financier'), ('developer', 'Développeur'), ('designer', 'Designer'), ('marketing', 'Marketing'), ('sales', 'Commercial'), ('other', 'Autre')], max_length=20, verbose_name='Rôle souhaité')),
                ('motivation', models.TextField(verbose_name='Motivation')),
                ('status', models.CharField(choices=[('pending', 'En attente'), ('accepted', 'Acceptée'), ('rejected', 'Rejetée')], default='pending', max_length=20, verbose_name='Statut')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('review_date', models.DateTimeField(blank=True, null=True)),
                ('review_notes', models.TextField(blank=True, verbose_name="Notes d'examen")),
                ('reviewed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_join_requests', to=settings.AUTH_USER_MODEL, verbose_name='Examiné par')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='startup_join_requests', to=settings.AUTH_USER_MODEL, verbose_name='Utilisateur')),
                ('startup', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='join_requests', to='startups.startup', verbose_name='Startup')),
            ],
            options={
                'verbose_name': 'Demande de participation',
                'verbose_name_plural': 'Demandes de participation',
                'ordering': ['-created_at'],
                'unique_together': {('startup', 'user', 'status')},
            },
        ),
    ]
