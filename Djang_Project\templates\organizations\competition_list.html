{% extends "organizations/base_organizations.html" %}

{% block title %}Compétitions | {{ block.super }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active">Compétitions</li>
{% endblock %}

{% block organization_content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1>Compétitions</h1>
    </div>
    <div class="col-md-4 text-end">
        {% if user.is_authenticated and user.is_staff %}
        <a href="{% url 'organizations:competition_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Créer une Compétition
        </a>
        {% endif %}
    </div>
</div>

<!-- Filtres -->
<div class="row mb-4">
    <div class="col-md-12">
        <form method="get" class="card card-body">
            <div class="row">
                <div class="col-md-4">
                    <label for="type" class="form-label">Type</label>
                    <select name="type" id="type" class="form-select">
                        <option value="">Tous</option>
                        <option value="pitch" {% if selected_type == 'pitch' %}selected{% endif %}>Pitch</option>
                        <option value="hackathon" {% if selected_type == 'hackathon' %}selected{% endif %}>Hackathon</option>
                        <option value="challenge" {% if selected_type == 'challenge' %}selected{% endif %}>Challenge</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="status" class="form-label">Statut</label>
                    <select name="status" id="status" class="form-select">
                        <option value="">Tous</option>
                        <option value="upcoming" {% if selected_status == 'upcoming' %}selected{% endif %}>À venir</option>
                        <option value="ongoing" {% if selected_status == 'ongoing' %}selected{% endif %}>En cours</option>
                        <option value="completed" {% if selected_status == 'completed' %}selected{% endif %}>Terminé</option>
                    </select>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">Filtrer</button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Liste des compétitions -->
<div class="row">
    {% for competition in competitions %}
    <div class="col-md-6 mb-4">
        <div class="card competition-card">
            <div class="card-body">
                <span class="status-badge badge {% if competition.status == 'upcoming' %}bg-info{% elif competition.status == 'ongoing' %}bg-success{% elif competition.status == 'completed' %}bg-secondary{% else %}bg-danger{% endif %}">
                    {{ competition.get_status_display }}
                </span>
                <h5 class="card-title mb-3">{{ competition.title }}</h5>
                <p class="card-text text-muted">{{ competition.description|truncatewords:30 }}</p>
                
                <div class="mb-3">
                    <div class="row">
                        <div class="col-6">
                            <small class="text-muted">
                                <i class="fas fa-calendar"></i> Début:
                            </small><br>
                            {{ competition.start_date|date:"d M Y" }}
                        </div>
                        <div class="col-6">
                            <small class="text-muted">
                                <i class="fas fa-flag-checkered"></i> Fin:
                            </small><br>
                            {{ competition.end_date|date:"d M Y" }}
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <small class="text-muted">
                        <i class="fas fa-trophy"></i> Prix:
                    </small><br>
                    <span class="prize-amount">{{ competition.prize }}</span>
                </div>
                
                <div class="mb-3">
                    <small class="text-muted">
                        <i class="fas fa-users"></i> Participants:
                    </small><br>
                    <span class="participant-count">
                        {{ competition.registrations.count }} / {{ competition.max_participants }}
                    </span>
                </div>
                
                <div class="d-grid gap-2">
                    <a href="{% url 'organizations:competition_detail' competition.pk %}" class="btn btn-outline-primary">
                        En savoir plus
                    </a>
                    {% if user.is_authenticated and not user.is_staff %}
                        {% if user.startup and not competition.registrations.filter(startup=user.startup).exists and competition.status == 'upcoming' %}
                        <a href="{% url 'organizations:competition_register' competition.pk %}" class="btn btn-success">
                            S'inscrire
                        </a>
                        {% endif %}
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% empty %}
    <div class="col-12">
        <div class="alert alert-info">
            Aucune compétition ne correspond à vos critères de recherche.
        </div>
    </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if is_paginated %}
<nav aria-label="Page navigation" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if page_obj.has_previous %}
        <li class="page-item">
            <a class="page-link" href="?page=1">&laquo; Première</a>
        </li>
        <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Précédente</a>
        </li>
        {% endif %}

        <li class="page-item disabled">
            <span class="page-link">
                Page {{ page_obj.number }} sur {{ page_obj.paginator.num_pages }}
            </span>
        </li>

        {% if page_obj.has_next %}
        <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.next_page_number }}">Suivante</a>
        </li>
        <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">Dernière &raquo;</a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}
{% endblock %}
