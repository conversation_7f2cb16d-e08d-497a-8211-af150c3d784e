{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}Inscription - Community Lab{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><PERSON><PERSON><PERSON> un compte</h4>
                </div>
                <div class="card-body">
                    <form method="post" novalidate enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.username|crispy }}
                                {{ form.email|crispy }}
                                {{ form.password1|crispy }}
                                {{ form.password2|crispy }}
                            </div>
                            <div class="col-md-6">
                                {{ form.first_name|crispy }}
                                {{ form.last_name|crispy }}
                                {{ form.user_type|crispy }}
                                {{ form.profile_picture|crispy }}
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="terms" required>
                                <label class="form-check-label" for="terms">
                                    J'accepte les <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">conditions d'utilisation</a>
                                </label>
                            </div>
                        </div>

                        <div class="d-grid gap-2 mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-user-plus"></i> S'inscrire
                            </button>
                        </div>
                    </form>
                    
                    <hr>
                    
                    <div class="text-center">
                        <p>Vous avez déjà un compte ?</p>
                        <a href="{% url 'accounts:login' %}" class="btn btn-outline-primary">
                            <i class="fas fa-sign-in-alt"></i> Se connecter
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal des conditions d'utilisation -->
<div class="modal fade" id="termsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Conditions d'utilisation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>1. Acceptation des conditions</h6>
                <p>En créant un compte sur Community Lab, vous acceptez les présentes conditions d'utilisation...</p>
                
                <h6>2. Protection des données</h6>
                <p>Nous nous engageons à protéger vos données personnelles conformément à la réglementation en vigueur...</p>
                
                <h6>3. Responsabilités</h6>
                <p>En tant qu'utilisateur de la plateforme, vous vous engagez à...</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
