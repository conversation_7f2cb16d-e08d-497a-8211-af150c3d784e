{% extends 'base.html' %}

{% block title %}Soumettre un projet - {{ project_call.title }} - Community Lab{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{% url 'organizations:project_call_list' %}">Appels à projets</a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{% url 'organizations:project_call_detail' project_call.id %}">{{ project_call.title }}</a>
                </li>
                <li class="breadcrumb-item active">Soumettre un projet</li>
            </ol>
        </nav>

        <div class="card">
            <div class="card-body">
                <h1 class="card-title mb-4">Soumettre un projet</h1>

                <div class="alert alert-info mb-4">
                    <h5 class="alert-heading">
                        <i class="fas fa-info-circle"></i> Informations importantes
                    </h5>
                    <p class="mb-0">
                        Date limite de soumission : <strong>{{ project_call.deadline|date:"d/m/Y" }}</strong><br>
                        Financement disponible : <strong>{{ project_call.funding_amount }} BIF</strong>
                    </p>
                </div>

                <form method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}

                    <div class="mb-4">
                        <h5>Informations du projet</h5>
                        
                        <div class="mb-3">
                            <label for="{{ form.title.id_for_label }}" class="form-label">
                                Titre du projet *
                            </label>
                            {{ form.title }}
                            {% if form.title.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.title.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.title.help_text %}
                                <div class="form-text">{{ form.title.help_text }}</div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                Description détaillée *
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.description.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.description.help_text %}
                                <div class="form-text">{{ form.description.help_text }}</div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.objectives.id_for_label }}" class="form-label">
                                Objectifs du projet *
                            </label>
                            {{ form.objectives }}
                            {% if form.objectives.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.objectives.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.objectives.help_text %}
                                <div class="form-text">{{ form.objectives.help_text }}</div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.impact.id_for_label }}" class="form-label">
                                Impact attendu *
                            </label>
                            {{ form.impact }}
                            {% if form.impact.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.impact.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.impact.help_text %}
                                <div class="form-text">{{ form.impact.help_text }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="mb-4">
                        <h5>Budget et ressources</h5>
                        
                        <div class="mb-3">
                            <label for="{{ form.requested_funding.id_for_label }}" class="form-label">
                                Financement demandé (BIF) *
                            </label>
                            {{ form.requested_funding }}
                            {% if form.requested_funding.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.requested_funding.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.requested_funding.help_text %}
                                <div class="form-text">{{ form.requested_funding.help_text }}</div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.budget_breakdown.id_for_label }}" class="form-label">
                                Détail du budget *
                            </label>
                            {{ form.budget_breakdown }}
                            {% if form.budget_breakdown.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.budget_breakdown.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.budget_breakdown.help_text %}
                                <div class="form-text">{{ form.budget_breakdown.help_text }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="mb-4">
                        <h5>Documents</h5>
                        
                        <div class="mb-3">
                            <label for="{{ form.presentation.id_for_label }}" class="form-label">
                                Présentation du projet (PDF) *
                            </label>
                            {{ form.presentation }}
                            {% if form.presentation.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.presentation.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.presentation.help_text %}
                                <div class="form-text">{{ form.presentation.help_text }}</div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.additional_documents.id_for_label }}" class="form-label">
                                Documents supplémentaires
                            </label>
                            {{ form.additional_documents }}
                            {% if form.additional_documents.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.additional_documents.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.additional_documents.help_text %}
                                <div class="form-text">{{ form.additional_documents.help_text }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="form-check mb-4">
                        {{ form.terms_accepted }}
                        <label class="form-check-label" for="{{ form.terms_accepted.id_for_label }}">
                            J'accepte les conditions de participation et certifie que les informations fournies sont exactes *
                        </label>
                        {% if form.terms_accepted.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.terms_accepted.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{% url 'organizations:project_call_detail' project_call.id %}" 
                           class="btn btn-outline-secondary">
                            Annuler
                        </a>
                        <button type="submit" class="btn btn-primary">
                            Soumettre le projet
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
{% endblock %}

{% block extra_js %}
<script>
    // Activation de la validation des formulaires Bootstrap
    (function () {
        'use strict'
        var forms = document.querySelectorAll('.needs-validation')
        Array.prototype.slice.call(forms).forEach(function (form) {
            form.addEventListener('submit', function (event) {
                if (!form.checkValidity()) {
                    event.preventDefault()
                    event.stopPropagation()
                }
                form.classList.add('was-validated')
            }, false)
        })
    })()
</script>
{% endblock %}
