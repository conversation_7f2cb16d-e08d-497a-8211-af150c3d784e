{% extends 'base.html' %}

{% block title %}{{ course.title }} - Apprentissage - Community Lab{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-3">
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="card-title mb-0">Programme</h5>
                    <span class="badge bg-primary">{{ enrollment.progress }}%</span>
                </div>
                
                <div class="progress mb-4">
                    <div class="progress-bar" role="progressbar" 
                         style="width: {{ enrollment.progress }}%" 
                         aria-valuenow="{{ enrollment.progress }}" 
                         aria-valuemin="0" 
                         aria-valuemax="100">
                    </div>
                </div>

                <div class="list-group list-group-flush course-navigation">
                    {% for module in course.modules.all %}
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center module-header" 
                                 data-bs-toggle="collapse" 
                                 data-bs-target="#module{{ module.id }}">
                                <h6 class="mb-0">{{ module.title }}</h6>
                                <span class="badge bg-secondary">
                                    {{ module.lessons.count }} leçons
                                </span>
                            </div>
                            <div class="collapse {% if module == current_module %}show{% endif %}" 
                                 id="module{{ module.id }}">
                                <div class="list-group list-group-flush mt-2">
                                    {% for lesson in module.lessons.all %}
                                        <a href="{% url 'learning:lesson_detail' course.id module.id lesson.id %}" 
                                           class="list-group-item list-group-item-action {% if lesson == current_lesson %}active{% endif %} {% if lesson in completed_lessons %}completed{% endif %}">
                                            <div class="d-flex align-items-center">
                                                {% if lesson in completed_lessons %}
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                {% elif lesson == current_lesson %}
                                                    <i class="fas fa-play-circle text-primary me-2"></i>
                                                {% else %}
                                                    <i class="far fa-circle me-2"></i>
                                                {% endif %}
                                                <div>
                                                    <div class="lesson-title">{{ lesson.title }}</div>
                                                    <small class="text-muted">{{ lesson.duration }} min</small>
                                                </div>
                                            </div>
                                        </a>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        {% if course.resources.exists %}
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Ressources</h5>
                    <div class="list-group list-group-flush">
                        {% for resource in course.resources.all %}
                            <a href="{{ resource.file.url }}" 
                               class="list-group-item list-group-item-action" 
                               download>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-file-alt me-2"></i>
                                    <div>
                                        <div>{{ resource.title }}</div>
                                        <small class="text-muted">
                                            {{ resource.file.size|filesizeformat }}
                                        </small>
                                    </div>
                                </div>
                            </a>
                        {% endfor %}
                    </div>
                </div>
            </div>
        {% endif %}
    </div>

    <div class="col-md-9">
        <div class="card mb-4">
            <div class="card-body">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="{% url 'learning:course_detail' course.id %}">{{ course.title }}</a>
                        </li>
                        <li class="breadcrumb-item">{{ current_module.title }}</li>
                        <li class="breadcrumb-item active">{{ current_lesson.title }}</li>
                    </ol>
                </nav>

                <div class="lesson-content mb-4">
                    {% if current_lesson.video_url %}
                        <div class="ratio ratio-16x9 mb-4">
                            <iframe src="{{ current_lesson.video_url }}" 
                                    allowfullscreen 
                                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture">
                            </iframe>
                        </div>
                    {% endif %}

                    <h1>{{ current_lesson.title }}</h1>
                    {{ current_lesson.content|safe }}

                    {% if current_lesson.attachments.exists %}
                        <div class="mt-4">
                            <h5>Pièces jointes</h5>
                            <div class="list-group">
                                {% for attachment in current_lesson.attachments.all %}
                                    <a href="{{ attachment.file.url }}" 
                                       class="list-group-item list-group-item-action" 
                                       download>
                                        <i class="fas fa-paperclip me-2"></i>
                                        {{ attachment.title }}
                                    </a>
                                {% endfor %}
                            </div>
                        </div>
                    {% endif %}
                </div>

                <div class="d-flex justify-content-between align-items-center">
                    {% if previous_lesson %}
                        <a href="{% url 'learning:lesson_detail' course.id current_module.id previous_lesson.id %}" 
                           class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left"></i> Leçon précédente
                        </a>
                    {% else %}
                        <div></div>
                    {% endif %}

                    {% if current_lesson not in completed_lessons %}
                        <form method="post" action="{% url 'learning:mark_lesson_complete' course.id current_lesson.id %}" 
                              class="d-inline">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-success">
                                Marquer comme terminé
                            </button>
                        </form>
                    {% endif %}

                    {% if next_lesson %}
                        <a href="{% url 'learning:lesson_detail' course.id current_module.id next_lesson.id %}" 
                           class="btn btn-outline-primary">
                            Leçon suivante <i class="fas fa-arrow-right"></i>
                        </a>
                    {% else %}
                        {% if current_module.is_last %}
                            <form method="post" action="{% url 'learning:complete_course' course.id %}" 
                                  class="d-inline">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-success">
                                    Terminer le cours
                                </button>
                            </form>
                        {% endif %}
                    {% endif %}
                </div>
            </div>
        </div>

        {% if current_lesson.quiz %}
            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title">Quiz - {{ current_lesson.title }}</h5>
                    <form method="post" action="{% url 'learning:submit_quiz' course.id current_lesson.id %}">
                        {% csrf_token %}
                        {% for question in current_lesson.quiz.questions.all %}
                            <div class="mb-4">
                                <p class="fw-bold mb-2">{{ forloop.counter }}. {{ question.text }}</p>
                                {% for choice in question.choices.all %}
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" 
                                               name="question_{{ question.id }}" 
                                               id="choice_{{ choice.id }}" 
                                               value="{{ choice.id }}" required>
                                        <label class="form-check-label" for="choice_{{ choice.id }}">
                                            {{ choice.text }}
                                        </label>
                                    </div>
                                {% endfor %}
                            </div>
                        {% endfor %}
                        <button type="submit" class="btn btn-primary">
                            Soumettre le quiz
                        </button>
                    </form>
                </div>
            </div>
        {% endif %}

        {% if current_lesson.assignment %}
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Devoir - {{ current_lesson.title }}</h5>
                    <div class="mb-4">
                        {{ current_lesson.assignment.description|safe }}
                    </div>

                    {% if current_lesson.assignment.submission %}
                        <div class="alert alert-info">
                            <h6>Votre soumission</h6>
                            <p class="mb-2">
                                Soumis le {{ current_lesson.assignment.submission.submitted_at|date:"d/m/Y H:i" }}
                            </p>
                            {% if current_lesson.assignment.submission.feedback %}
                                <div class="mt-3">
                                    <h6>Feedback du mentor</h6>
                                    {{ current_lesson.assignment.submission.feedback|linebreaks }}
                                </div>
                            {% endif %}
                        </div>
                    {% else %}
                        <form method="post" action="{% url 'learning:submit_assignment' course.id current_lesson.id %}" 
                              enctype="multipart/form-data">
                            {% csrf_token %}
                            {{ assignment_form|crispy }}
                            <button type="submit" class="btn btn-primary">
                                Soumettre le devoir
                            </button>
                        </form>
                    {% endif %}
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<style>
    .course-navigation .module-header {
        cursor: pointer;
    }
    .course-navigation .module-header:hover {
        background-color: rgba(0,0,0,.03);
    }
    .lesson-title {
        font-size: 0.9rem;
    }
    .completed .lesson-title {
        text-decoration: line-through;
        color: #28a745;
    }
    .list-group-item.active {
        background-color: #f8f9fa;
        border-color: rgba(0,0,0,.125);
        color: inherit;
    }
    .list-group-item.active .lesson-title {
        color: #007bff;
        font-weight: bold;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-expand current module
    const currentModule = document.querySelector('.collapse.show');
    if (currentModule) {
        currentModule.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    // Track video progress
    const videoFrame = document.querySelector('.lesson-content iframe');
    if (videoFrame) {
        // Add video tracking logic here
    }

    // Quiz validation
    const quizForm = document.querySelector('form[action*="submit_quiz"]');
    if (quizForm) {
        quizForm.addEventListener('submit', function(event) {
            const unanswered = this.querySelectorAll('input[type="radio"]:not(:checked)');
            if (unanswered.length === this.querySelectorAll('.form-check-input').length) {
                event.preventDefault();
                alert('Veuillez répondre à toutes les questions du quiz.');
            }
        });
    }
});
</script>
{% endblock %}
