{% extends 'base.html' %}

{% block title %}Analytiques des compétitions - Community Lab{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-md-12">
            <h1>Analytiques des compétitions</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'analytics:dashboard' %}">Tableau de bord</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Compétitions</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row">
        {% for metric in metrics %}
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ metric.competition.title }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <h6 class="text-muted">Inscriptions totales</h6>
                                <h2>{{ metric.total_registrations }}</h2>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <h6 class="text-muted">Participants confirmés</h6>
                                <h2>{{ metric.confirmed_participants }}</h2>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <h6 class="text-muted">Taux de participation</h6>
                                <h2>{{ metric.participation_rate|floatformat:1 }}%</h2>
                            </div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h6>Taux de confirmation</h6>
                        <div class="progress" style="height: 10px;">
                            {% if metric.total_registrations > 0 %}
                            <div class="progress-bar bg-success" role="progressbar" 
                                 style="width: {% widthratio metric.confirmed_participants metric.total_registrations 100 %}%"
                                 aria-valuenow="{% widthratio metric.confirmed_participants metric.total_registrations 100 %}" 
                                 aria-valuemin="0" aria-valuemax="100">
                            </div>
                            {% else %}
                            <div class="progress-bar" role="progressbar" style="width: 0%"
                                 aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="mt-4">
                        <h6>Taux de participation</h6>
                        <div class="progress" style="height: 10px;">
                            <div class="progress-bar bg-info" role="progressbar" 
                                 style="width: {{ metric.participation_rate }}%"
                                 aria-valuenow="{{ metric.participation_rate }}" 
                                 aria-valuemin="0" aria-valuemax="100">
                            </div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">Dernière mise à jour : {{ metric.last_updated|date:"d/m/Y H:i" }}</small>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{% url 'organizations:competition_detail' metric.competition.id %}" class="btn btn-sm btn-primary">
                        <i class="fas fa-eye"></i> Voir la compétition
                    </a>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="alert alert-info">
                Aucune donnée analytique disponible pour le moment.
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}
