from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('notifs', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='notificationpreference',
            name='push_notifications',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='notificationpreference',
            name='email_notifications',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='notificationpreference',
            name='course_updates',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='notificationpreference',
            name='competition_updates',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='notificationpreference',
            name='project_updates',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='notificationpreference',
            name='startup_updates',
            field=models.<PERSON><PERSON>an<PERSON>ield(default=True),
        ),
        migrations.Add<PERSON><PERSON>(
            model_name='notificationpreference',
            name='mentorship_updates',
            field=models.<PERSON><PERSON><PERSON><PERSON><PERSON>(default=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='notificationpreference',
            name='system_updates',
            field=models.<PERSON>olean<PERSON>ield(default=True),
        ),
    ]
