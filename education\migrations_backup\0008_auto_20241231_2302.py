# Generated by Django 4.2.17 on 2024-12-31 21:02

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('education', '0007_auto_20241231_2300'),
    ]

    operations = [
        migrations.CreateModel(
            name='NewEnrollment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('enrollment_date', models.DateTimeField(auto_now_add=True)),
                ('completion_date', models.DateTimeField(blank=True, null=True)),
                ('certificate_issued', models.BooleanField(default=False)),
                ('feedback', models.TextField(blank=True)),
                ('rating', models.IntegerField(blank=True, null=True)),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='new_enrollments', to=settings.AUTH_USER_MODEL)),
                ('training', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='new_enrollments', to='education.training')),
            ],
            options={
                'ordering': ['-enrollment_date'],
                'unique_together': {('student', 'training')},
            },
        ),
        migrations.DeleteModel(
            name='Enrollment',
        ),
        migrations.RenameModel(
            old_name='NewEnrollment',
            new_name='Enrollment',
        ),
    ]
