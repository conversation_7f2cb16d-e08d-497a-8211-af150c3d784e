{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}Demande d'adhésion de {{ request.user.get_full_name }} - {{ organization.name }} - Community Lab{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{% url 'organizations:organization_list' %}">Organisations</a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{% url 'organizations:organization_detail' organization.id %}">
                        {{ organization.name }}
                    </a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{% url 'organizations:join_request_list' organization.id %}">
                        Demandes d'adhésion
                    </a>
                </li>
                <li class="breadcrumb-item active">{{ request.user.get_full_name }}</li>
            </ol>
        </nav>

        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div class="d-flex align-items-center">
                        {% if request.user.profile.avatar %}
                            <img src="{{ request.user.profile.avatar.url }}" 
                                 class="rounded-circle me-3" 
                                 style="width: 64px; height: 64px;" 
                                 alt="{{ request.user.get_full_name }}">
                        {% endif %}
                        <div>
                            <h1 class="h3 mb-1">{{ request.user.get_full_name }}</h1>
                            <p class="text-muted mb-0">
                                Demande envoyée le {{ request.created_at|date:"d/m/Y à H:i" }}
                            </p>
                        </div>
                    </div>
                    <span class="badge {% if request.status == 'PENDING' %}bg-warning{% elif request.status == 'APPROVED' %}bg-success{% else %}bg-danger{% endif %} fs-6">
                        {{ request.get_status_display }}
                    </span>
                </div>

                <div class="mb-4">
                    <h5>Profil du demandeur</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-2">
                                <strong>Email :</strong>
                                <a href="mailto:{{ request.user.email }}">{{ request.user.email }}</a>
                            </p>
                            <p class="mb-2">
                                <strong>Membre depuis :</strong>
                                {{ request.user.date_joined|date:"F Y" }}
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-2">
                                <strong>Profession :</strong>
                                {{ request.user.profile.title|default:"Non spécifié" }}
                            </p>
                            <p class="mb-2">
                                <strong>Localisation :</strong>
                                {{ request.user.profile.location|default:"Non spécifié" }}
                            </p>
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <h5>Motivation</h5>
                    <div class="card bg-light">
                        <div class="card-body">
                            {{ request.motivation|linebreaks }}
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <h5>Expérience</h5>
                    <div class="card bg-light">
                        <div class="card-body">
                            {{ request.experience|linebreaks }}
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <h5>Contribution potentielle</h5>
                    <div class="card bg-light">
                        <div class="card-body">
                            {{ request.contribution|linebreaks }}
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <h5>Disponibilité</h5>
                    <div class="card bg-light">
                        <div class="card-body">
                            {{ request.availability|linebreaks }}
                        </div>
                    </div>
                </div>

                {% if request.status == 'PENDING' and user.is_organization_admin %}
                    <div class="mb-4">
                        <h5>Évaluation</h5>
                        <form method="post" action="{% url 'organizations:join_request_review' organization.id request.id %}">
                            {% csrf_token %}
                            {{ review_form|crispy }}
                            <div class="d-flex justify-content-between mt-3">
                                <button type="submit" name="action" value="reject" 
                                        class="btn btn-danger" 
                                        onclick="return confirm('Êtes-vous sûr de vouloir refuser cette demande ?');">
                                    <i class="fas fa-times"></i> Refuser
                                </button>
                                <button type="submit" name="action" value="approve" 
                                        class="btn btn-success">
                                    <i class="fas fa-check"></i> Approuver
                                </button>
                            </div>
                        </form>
                    </div>
                {% endif %}

                {% if request.reviewed_by %}
                    <div class="mb-4">
                        <h5>Décision</h5>
                        <div class="card bg-light">
                            <div class="card-body">
                                <p class="mb-2">
                                    <strong>Décision prise par :</strong>
                                    {{ request.reviewed_by.get_full_name }}
                                </p>
                                <p class="mb-2">
                                    <strong>Date de décision :</strong>
                                    {{ request.reviewed_at|date:"d/m/Y à H:i" }}
                                </p>
                                {% if request.review_notes %}
                                    <p class="mb-0">
                                        <strong>Notes :</strong><br>
                                        {{ request.review_notes|linebreaks }}
                                    </p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                {% endif %}

                <div class="d-flex justify-content-between">
                    <a href="{% url 'organizations:join_request_list' organization.id %}" 
                       class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Retour à la liste
                    </a>
                    <a href="{% url 'accounts:profile_detail' request.user.username %}" 
                       class="btn btn-outline-primary">
                        <i class="fas fa-user"></i> Voir le profil complet
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        {% if user.is_organization_admin and request.status == 'PENDING' %}
            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title">Guide d'évaluation</h5>
                    <div class="mb-3">
                        <h6>Points à considérer :</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-check-circle text-success"></i>
                                Pertinence de la motivation
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check-circle text-success"></i>
                                Expérience dans le domaine
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check-circle text-success"></i>
                                Valeur ajoutée potentielle
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check-circle text-success"></i>
                                Disponibilité et engagement
                            </li>
                        </ul>
                    </div>
                    <div class="alert alert-info mb-0">
                        <i class="fas fa-info-circle"></i>
                        Une fois approuvée, la personne deviendra automatiquement membre de l'organisation.
                    </div>
                </div>
            </div>
        {% endif %}

        {% if similar_requests %}
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Autres demandes récentes</h5>
                    <div class="list-group list-group-flush">
                        {% for similar in similar_requests %}
                            <a href="{% url 'organizations:join_request_detail' organization.id similar.id %}" 
                               class="list-group-item list-group-item-action">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">{{ similar.user.get_full_name }}</h6>
                                        <small class="text-muted">
                                            {{ similar.created_at|date:"d/m/Y" }}
                                        </small>
                                    </div>
                                    <span class="badge {% if similar.status == 'PENDING' %}bg-warning{% elif similar.status == 'APPROVED' %}bg-success{% else %}bg-danger{% endif %}">
                                        {{ similar.get_status_display }}
                                    </span>
                                </div>
                            </a>
                        {% endfor %}
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
{% endblock %}
