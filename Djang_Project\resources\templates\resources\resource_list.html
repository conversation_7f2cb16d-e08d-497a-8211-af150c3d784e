{% extends "base.html" %}
{% load static %}

{% block title %}Ressources{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Ressources</h1>
        {% if user.is_authenticated %}
        <a href="{% url 'resources:resource-create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Ajouter une ressource
        </a>
        {% endif %}
    </div>

    <!-- Filtres -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="category" class="form-label">Catégorie</label>
                    <select name="category" id="category" class="form-select">
                        <option value="">Toutes les catégories</option>
                        {% for category in categories %}
                        <option value="{{ category.slug }}" {% if request.GET.category == category.slug %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="resource_type" class="form-label">Type de ressource</label>
                    <select name="type" id="resource_type" class="form-select">
                        <option value="">Tous les types</option>
                        {% for type in resource_types %}
                        <option value="{{ type.0 }}" {% if request.GET.type == type.0 %}selected{% endif %}>
                            {{ type.1 }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="tag" class="form-label">Tag</label>
                    <select name="tag" id="tag" class="form-select">
                        <option value="">Tous les tags</option>
                        {% for tag in tags %}
                        <option value="{{ tag.slug }}" {% if request.GET.tag == tag.slug %}selected{% endif %}>
                            {{ tag.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">Filtrer</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Liste des ressources -->
    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
        {% for resource in resources %}
        <div class="col">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h5 class="card-title mb-0">{{ resource.title }}</h5>
                        {% if user.is_authenticated %}
                        <button class="btn btn-outline-primary btn-sm bookmark-btn" data-resource-id="{{ resource.id }}"
                            title="{% if resource.is_bookmarked %}Retirer des favoris{% else %}Ajouter aux favoris{% endif %}">
                            <i class="fas fa-bookmark{% if not resource.is_bookmarked %}-o{% endif %}"></i>
                        </button>
                        {% endif %}
                    </div>
                    <p class="card-text text-muted small">
                        <i class="fas fa-folder"></i> {{ resource.category.name }}
                        <span class="mx-2">|</span>
                        <i class="fas fa-file-alt"></i> {{ resource.get_resource_type_display }}
                    </p>
                    <p class="card-text">{{ resource.description|truncatewords:30 }}</p>
                    <div class="mb-3">
                        {% for tag in resource.tags.all %}
                        <span class="badge bg-secondary me-1">{{ tag.name }}</span>
                        {% endfor %}
                    </div>
                </div>
                <div class="card-footer bg-transparent">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fas fa-download"></i> {{ resource.download_count }} téléchargements
                        </small>
                        {% if resource.file %}
                        <a href="{% url 'resources:resource-download' resource.id %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-download"></i> Télécharger
                        </a>
                        {% elif resource.url %}
                        <a href="{{ resource.url }}" target="_blank" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-external-link-alt"></i> Visiter
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="alert alert-info" role="alert">
                Aucune ressource trouvée.
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <nav aria-label="Page navigation" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
            {% endif %}

            {% for num in page_obj.paginator.page_range %}
            <li class="page-item {% if page_obj.number == num %}active{% endif %}">
                <a class="page-link" href="?page={{ num }}">{{ num }}</a>
            </li>
            {% endfor %}

            {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const bookmarkButtons = document.querySelectorAll('.bookmark-btn');
    
    bookmarkButtons.forEach(button => {
        button.addEventListener('click', function() {
            const resourceId = this.dataset.resourceId;
            fetch(`/api/v1/resources/${resourceId}/bookmark/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                }
            })
            .then(response => response.json())
            .then(data => {
                const icon = this.querySelector('i');
                if (data.status === 'bookmark added') {
                    icon.classList.remove('fa-bookmark-o');
                    icon.classList.add('fa-bookmark');
                    this.title = 'Retirer des favoris';
                } else {
                    icon.classList.remove('fa-bookmark');
                    icon.classList.add('fa-bookmark-o');
                    this.title = 'Ajouter aux favoris';
                }
            });
        });
    });
});
</script>
{% endblock %}
{% endblock %}
