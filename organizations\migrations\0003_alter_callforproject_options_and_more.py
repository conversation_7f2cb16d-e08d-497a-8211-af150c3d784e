# Generated by Django 4.2.8 on 2024-12-30 07:48

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('organizations', '0002_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='callforproject',
            options={'ordering': ['start_date']},
        ),
        migrations.AlterModelOptions(
            name='competition',
            options={'ordering': ['start_date']},
        ),
        migrations.AlterModelOptions(
            name='organization',
            options={'ordering': ['name']},
        ),
        migrations.AlterModelOptions(
            name='submission',
            options={'ordering': ['-submitted_at']},
        ),
        migrations.RemoveField(
            model_name='callforproject',
            name='budget_range',
        ),
        migrations.RemoveField(
            model_name='callforproject',
            name='created_at',
        ),
        migrations.RemoveField(
            model_name='callforproject',
            name='updated_at',
        ),
        migrations.RemoveField(
            model_name='competition',
            name='created_at',
        ),
        migrations.RemoveField(
            model_name='competition',
            name='event_end',
        ),
        migrations.RemoveField(
            model_name='competition',
            name='event_start',
        ),
        migrations.RemoveField(
            model_name='competition',
            name='prizes',
        ),
        migrations.RemoveField(
            model_name='competition',
            name='registration_end',
        ),
        migrations.RemoveField(
            model_name='competition',
            name='registration_start',
        ),
        migrations.RemoveField(
            model_name='competition',
            name='updated_at',
        ),
        migrations.RemoveField(
            model_name='organization',
            name='address',
        ),
        migrations.RemoveField(
            model_name='organization',
            name='created_at',
        ),
        migrations.RemoveField(
            model_name='organization',
            name='updated_at',
        ),
        migrations.RemoveField(
            model_name='submission',
            name='call_for_project',
        ),
        migrations.RemoveField(
            model_name='submission',
            name='updated_at',
        ),
        migrations.AddField(
            model_name='callforproject',
            name='budget_max',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True),
        ),
        migrations.AddField(
            model_name='callforproject',
            name='budget_min',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True),
        ),
        migrations.AddField(
            model_name='competition',
            name='end_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='competition',
            name='max_participants',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='competition',
            name='prize_pool',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True),
        ),
        migrations.AddField(
            model_name='competition',
            name='start_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='organization',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='organization',
            name='location',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='organization',
            name='sector',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='organization',
            name='type',
            field=models.CharField(blank=True, choices=[('company', 'Company'), ('university', 'University'), ('ngo', 'NGO'), ('government', 'Government'), ('other', 'Other')], max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='submission',
            name='feedback',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='submission',
            name='score',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True),
        ),
        migrations.AddField(
            model_name='submission',
            name='url',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='callforproject',
            name='end_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='callforproject',
            name='organization',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='call_for_projects', to='organizations.organization'),
        ),
        migrations.AlterField(
            model_name='callforproject',
            name='start_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='callforproject',
            name='status',
            field=models.CharField(choices=[('draft', 'Draft'), ('open', 'Open'), ('closed', 'Closed'), ('cancelled', 'Cancelled')], default='draft', max_length=20),
        ),
        migrations.AlterField(
            model_name='callforproject',
            name='title',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='competition',
            name='status',
            field=models.CharField(choices=[('draft', 'Draft'), ('open', 'Open'), ('in_progress', 'In Progress'), ('judging', 'Judging'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='draft', max_length=20),
        ),
        migrations.AlterField(
            model_name='competition',
            name='title',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='organization',
            name='contact_email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AlterField(
            model_name='organization',
            name='contact_phone',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='organization',
            name='description',
            field=models.TextField(blank=True),
        ),
        migrations.AlterField(
            model_name='organization',
            name='name',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='organization',
            name='website',
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='submission',
            name='competition',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='submissions', to='organizations.competition'),
        ),
        migrations.AlterField(
            model_name='submission',
            name='file',
            field=models.FileField(blank=True, null=True, upload_to='competition_submissions/'),
        ),
        migrations.AlterField(
            model_name='submission',
            name='status',
            field=models.CharField(choices=[('draft', 'Draft'), ('submitted', 'Submitted'), ('under_review', 'Under Review'), ('accepted', 'Accepted'), ('rejected', 'Rejected')], default='draft', max_length=20),
        ),
        migrations.AlterField(
            model_name='submission',
            name='submitted_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='submission',
            name='submitter',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='competition_submissions', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='submission',
            name='title',
            field=models.CharField(max_length=255),
        ),
    ]
